# AugmentCode SAFit评估工具使用指南

## 🎯 **任务目标**
作为AugmentCode，请帮助用户使用cp文件夹中的SAFit评估工具对目标检测算法进行小目标检测性能评估。SAFit是比传统IoU更适合小目标检测的评估指标。

## 📁 **工具包内容**
cp文件夹包含完整的SAFit评估工具包：
- `universal_safit_evaluator.py` - 主评估脚本
- `config_template.yaml` - 配置模板
- `codes/` - RGBT-Tiny评估代码库
- `test_integration.py` - 测试脚本
- 相关文档和指南

## 🔍 **执行流程**

### **阶段1: 项目分析**
使用`view`工具分析当前项目：

1. **查看项目结构**
```
使用view工具查看当前目录结构，识别：
- 模型权重文件位置 (best.pt, latest.pth, model.pth等)
- 数据集目录结构 (images, labels等)
- 配置文件 (如果有)
- 模型类型线索 (requirements.txt, 代码文件等)
```

2. **识别算法类型**
```
通过代码文件判断算法类型：
- YOLOv5/v8: 查找yolo相关文件
- MMDetection: 查找mmdet相关导入
- 自定义PyTorch: 查找自定义模型定义
- 双模态: 查找RGB-T或thermal相关代码
```

3. **分析数据格式**
```
检查数据集格式：
- 图像文件格式和位置
- 标签格式 (YOLO, COCO等)
- 类别数量和名称
- 是否包含热红外图像
```

### **阶段2: 工具部署**
1. **复制工具文件**
```bash
cp -r cp/* ./
```

2. **创建配置文件**
基于项目分析结果，修改`config_template.yaml`创建`config.yaml`：

```yaml
# 根据分析结果填写
device: 'cuda'  # 或 'cpu'
model_path: './实际模型路径'
test_images_dir: './实际图像路径'
gt_labels_dir: './实际标签路径'
class_names: ['实际类别名称']
dual_modal: false  # 根据算法类型设置
```

### **阶段3: 适配和测试**
1. **运行集成测试**
```bash
python test_integration.py
```

2. **处理适配问题**
如果测试失败，可能需要：
- 修改模型加载逻辑
- 调整预测结果转换格式
- 处理特殊的输入输出格式

### **阶段4: 执行评估**
```bash
python universal_safit_evaluator.py --config config.yaml
```

## 🔧 **常见适配场景**

### **YOLOv5/YOLOv8项目**
```yaml
model_path: './runs/train/exp/weights/best.pt'
model_type: 'yolo'
test_images_dir: './data/images/val'
gt_labels_dir: './data/labels/val'
```

### **MMDetection项目**
```yaml
model_path: './work_dirs/faster_rcnn/latest.pth'
model_type: 'mmdet'
mmdet_config: './configs/faster_rcnn_r50_fpn_1x_coco.py'
```

### **自定义PyTorch项目**
可能需要修改`universal_safit_evaluator.py`中的：
- `_create_model_architecture()` - 添加模型架构
- `_convert_to_yolo_format()` - 适配输出格式

### **双模态RGB-T项目**
```yaml
dual_modal: true
test_images_dir: './data/rgb'
thermal_images_dir: './data/thermal'
```

## 🐛 **问题诊断和解决**

### **模型加载问题**
```python
# 使用codebase-retrieval查找模型定义
# 检查模型权重文件格式
# 必要时修改_load_generic_model方法
```

### **数据格式问题**
```python
# 检查标签格式是否为YOLO格式
# 确认图像和标签文件名对应
# 验证坐标归一化是否正确
```

### **预测转换问题**
```python
# 在_convert_to_yolo_format中添加调试信息
# 根据模型输出格式调整转换逻辑
# 确保输出格式为: class_id confidence x_center y_center width height
```

## 📊 **结果验证**

### **检查输出文件**
```
evaluation_output/
├── predictions/          # 预测结果文件
│   ├── image1.txt
│   └── ...
└── evaluation_results_*.json  # 评估结果
```

### **验证结果合理性**
```
SAFit AP@[0.5:0.95]: 0.3-0.8 (合理范围)
SAFit AP_tiny: 通常低于总体AP
SAFit vs IoU: SAFit在小目标上通常更高
```

## 💡 **优化建议**

### **性能优化**
- 调整batch_size避免内存问题
- 使用GPU加速推理
- 对大数据集可以采样评估

### **结果分析**
- 重点关注SAFit_tiny和SAFit_small指标
- 对比SAFit和IoU的差异
- 分析不同类别的性能差异

## 📝 **报告生成**

评估完成后生成报告包含：
1. **配置信息**: 模型类型、数据集、参数设置
2. **主要结果**: SAFit AP@[0.5:0.95]等关键指标
3. **小目标性能**: SAFit_tiny, SAFit_small详细分析
4. **对比分析**: SAFit vs IoU的提升情况
5. **问题记录**: 遇到的问题和解决方案

## 🎯 **成功标准**

评估成功的标志：
- ✅ 项目分析准确完整
- ✅ 配置文件正确设置
- ✅ 模型成功加载和推理
- ✅ 预测结果格式正确
- ✅ SAFit评估正常完成
- ✅ 结果数值合理可信
- ✅ 生成完整评估报告

## 🔄 **迭代改进**

如果初次评估有问题：
1. 分析错误信息
2. 调整配置或代码
3. 重新测试验证
4. 记录解决方案
5. 完善适配代码

请按照此指南帮助用户完成SAFit评估，确保每个步骤都有详细的分析和验证。
