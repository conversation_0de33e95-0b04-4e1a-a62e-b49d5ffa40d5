# 🚀 SAFit评估工具快速使用提示

## 📋 **任务**
使用cp文件夹中的SAFit评估工具对当前目标检测算法进行小目标检测性能评估。

## ⚡ **快速执行**

### **1. 复制工具文件**
```bash
cp -r cp/* ./
```

### **2. 分析项目并配置**
请分析当前项目并修改`config_template.yaml`为`config.yaml`：

**必须修改的配置项：**
```yaml
# 模型权重路径 (找到best.pt或类似文件)
model_path: './runs/train/exp/weights/best.pt'

# 测试数据路径
test_images_dir: './data/test/images'
gt_labels_dir: './data/test/labels'

# 类别名称 (根据数据集修改)
class_names:
  - 'person'
  - 'car'
  - 'bicycle'

# 模态类型
dual_modal: false  # RGB-T算法设为true
```

### **3. 运行评估**
```bash
# 测试集成
python test_integration.py

# 执行评估
python universal_safit_evaluator.py --config config.yaml
```

## 🎯 **预期结果**
```
📈 SAFit评估结果
SAFit AP@[0.5:0.95]:  0.489  ← 主要指标
SAFit AP_tiny:       0.378  ← 小目标性能
IoU AP@[0.5:0.95]:    0.423  ← 对比指标
🎯 SAFit相比IoU提升: +15.6%
```

## 🔧 **常见算法适配**

**YOLOv5/v8:**
```yaml
model_path: './runs/train/exp/weights/best.pt'
model_type: 'yolo'
```

**MMDetection:**
```yaml
model_path: './work_dirs/model/latest.pth'
model_type: 'mmdet'
```

**自定义模型:**
```yaml
model_path: './checkpoints/best.pth'
model_type: 'torch'
```

**双模态RGB-T:**
```yaml
dual_modal: true
thermal_images_dir: './data/test/thermal'
```

## ⚠️ **故障排除**

**模型加载失败** → 检查model_path和model_type
**数据路径错误** → 确认图像和标签目录存在
**格式转换失败** → 可能需要自定义_convert_to_yolo_format方法

## ✅ **成功标准**
- 模型成功加载
- 生成预测结果文件
- 显示SAFit和IoU评估结果
- 结果数值在合理范围(0-1)

请执行以上步骤并报告评估结果！
