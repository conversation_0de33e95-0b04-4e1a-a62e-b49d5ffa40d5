# SAFit评估工具使用提示词

## 📋 **任务描述**
请使用cp文件夹中的SAFit评估工具对当前算法进行小目标检测性能评估。SAFit是专门针对小目标检测优化的评估指标，比传统IoU更准确。

## 📁 **工具文件说明**
cp文件夹包含以下评估工具：
- `universal_safit_evaluator.py` - 通用SAFit评估器主脚本
- `yoloeval.py` - YOLO格式评估器核心模块
- `config_template.yaml` - 配置文件模板
- `codes/` - RGBT-Tiny完整评估代码库
  - `codes/metric/yoloeval.py` - YOLO评估器
  - `codes/metric/cocoeval.py` - COCO评估器
  - `codes/loss/loss.py` - SAFit损失函数
  - `codes/auxiliary_codes/` - 辅助工具
- `test_integration.py` - 集成测试脚本
- `UNIVERSAL_USAGE_GUIDE.md` - 详细使用指南

## 🎯 **评估目标**
1. 加载已训练好的模型权重(best.pt或类似文件)
2. 对测试数据集进行推理
3. 使用SAFit指标评估小目标检测性能
4. 同时提供IoU指标对比
5. 生成详细的评估报告

## 🚀 **执行步骤**

### **步骤1: 复制评估工具**
```bash
# 将cp文件夹中的所有文件复制到当前算法目录
cp -r cp/* ./
```

### **步骤2: 分析当前项目结构**
请先分析当前算法项目的：
- 模型权重文件位置 (通常是best.pt, latest.pth等)
- 测试数据集目录结构
- 模型类型 (YOLOv5/v8, MMDetection, 自定义PyTorch等)
- 类别名称和数量
- 是否为双模态(RGB-T)算法

### **步骤3: 配置评估参数**
根据项目分析结果，修改`config_template.yaml`为`config.yaml`：

```yaml
# 基础配置
device: 'cuda'  # 或 'cpu'
output_dir: './evaluation_output'

# 模型配置
model_path: './path/to/best.pt'  # 修改为实际模型路径
model_type: 'auto'  # 'auto', 'yolo', 'mmdet', 'torch'

# 数据配置
test_images_dir: './data/test/images'  # 修改为实际测试图像路径
gt_labels_dir: './data/test/labels'    # 修改为实际GT标签路径

# 模态配置
dual_modal: false  # 如果是RGB-T算法设为true

# 类别配置 (根据实际数据集修改)
class_names:
  - 'class1'
  - 'class2'
  - 'class3'

# 评估配置
conf_threshold: 0.001
img_size: 640
```

### **步骤4: 验证集成**
运行测试脚本确保工具正常工作：
```bash
python test_integration.py
```

### **步骤5: 执行SAFit评估**
```bash
python universal_safit_evaluator.py --config config.yaml
```

## 🔧 **不同算法类型的适配指南**

### **YOLOv5/YOLOv8算法**
```yaml
model_path: './runs/train/exp/weights/best.pt'
model_type: 'yolo'
```

### **MMDetection算法**
```yaml
model_path: './work_dirs/model/latest.pth'
model_type: 'mmdet'
mmdet_config: './configs/model_config.py'
```

### **自定义PyTorch算法**
```yaml
model_path: './checkpoints/best_model.pth'
model_type: 'torch'
```

### **双模态RGB-T算法**
```yaml
dual_modal: true
test_images_dir: './data/test/rgb'
thermal_images_dir: './data/test/thermal'
```

## 📊 **预期输出结果**

评估完成后应显示类似结果：
```
📈 SAFit评估结果
================================================================================
SAFit AP@[0.5:0.95]:  0.489
SAFit AP@0.5:        0.678
SAFit AP@0.75:       0.423
SAFit AP_tiny:       0.378  # 极小目标性能
SAFit AP_small:      0.456  # 小目标性能

IoU AP@[0.5:0.95]:    0.423
IoU AP@0.5:          0.651
IoU AP@0.75:         0.398
IoU AP_tiny:         0.291
IoU AP_small:        0.398

🎯 SAFit相比IoU提升: +15.6%
```

## ⚠️ **常见问题处理**

### **模型加载失败**
- 检查模型文件路径是否正确
- 确认模型类型设置是否匹配
- 查看错误信息调整model_type

### **数据路径错误**
- 确认测试图像目录存在
- 确认GT标签目录存在且格式为YOLO
- 检查文件名是否对应

### **预测结果转换失败**
- 如果是自定义模型，可能需要修改`_convert_to_yolo_format`方法
- 添加调试信息查看模型输出格式
- 参考UNIVERSAL_USAGE_GUIDE.md中的自定义适配部分

### **双模态图像未找到**
- 确认thermal_images_dir路径正确
- 确认RGB和热红外图像文件名对应

## 🎯 **评估质量检查**

评估完成后请检查：
1. **预测文件生成**: `evaluation_output/predictions/`目录应包含所有测试图像的预测结果
2. **结果合理性**: SAFit和IoU结果应在合理范围内(0-1)
3. **小目标性能**: 重点关注SAFit_tiny和SAFit_small指标
4. **对比分析**: SAFit通常在小目标上比IoU表现更好

## 📝 **结果报告建议**

生成评估报告时建议包含：
1. **主要指标**: SAFit AP@[0.5:0.95]作为主要性能指标
2. **小目标性能**: 重点报告SAFit_tiny和SAFit_small
3. **对比分析**: 与IoU指标的对比结果
4. **改进幅度**: SAFit相比IoU的提升百分比

## 🔄 **如果需要自定义修改**

如果当前算法有特殊需求，可能需要修改：
1. **模型加载**: 在`_load_generic_model`方法中添加特定加载逻辑
2. **预处理**: 在`_preprocess_image`方法中调整图像预处理
3. **后处理**: 在`_convert_to_yolo_format`方法中适配输出格式
4. **双模态推理**: 在`_predict_dual_modal`方法中实现双模态推理逻辑

## ✅ **成功标准**

评估成功的标志：
- ✅ 测试脚本通过
- ✅ 模型成功加载
- ✅ 预测结果正常生成
- ✅ SAFit和IoU评估完成
- ✅ 结果数值合理
- ✅ 评估报告保存成功

## 📞 **技术支持**

如果遇到问题：
1. 查看`UNIVERSAL_USAGE_GUIDE.md`获取详细说明
2. 运行`test_integration.py`进行诊断
3. 检查错误信息并根据常见问题部分处理
4. 必要时修改相应的适配代码

请按照以上步骤执行SAFit评估，并报告评估结果和遇到的任何问题。
