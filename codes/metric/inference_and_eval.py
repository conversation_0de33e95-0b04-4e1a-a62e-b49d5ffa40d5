#!/usr/bin/env python3
"""
从训练好的模型到评估的完整流程
支持YOLOv5/YOLOv8/YOLOv10等模型的best.pt文件
"""

import os
import sys
import torch
import cv2
import numpy as np
from pathlib import Path
from yoloeval import YOL<PERSON>val


def detect_yolo_version(model_path):
    """
    检测YOLO模型版本
    """
    try:
        model = torch.load(model_path, map_location='cpu')
        
        # 检查模型结构特征
        if 'model' in model:
            model_info = str(model['model'])
            if 'YOLOv5' in model_info or 'v5' in model_info:
                return 'yolov5'
            elif 'YOLOv8' in model_info or 'v8' in model_info:
                return 'yolov8'
            elif 'YOLOv10' in model_info or 'v10' in model_info:
                return 'yolov10'
        
        # 默认尝试ultralytics格式
        return 'ultralytics'
    except:
        return 'unknown'


def load_yolo_model(model_path):
    """
    加载YOLO模型
    """
    yolo_version = detect_yolo_version(model_path)
    print(f"检测到模型版本: {yolo_version}")
    
    try:
        if yolo_version in ['yolov8', 'yolov10', 'ultralytics']:
            # YOLOv8/v10 使用ultralytics
            from ultralytics import YOLO
            model = YOLO(model_path)
            print("✅ 使用ultralytics加载模型成功")
            return model, 'ultralytics'
            
        elif yolo_version == 'yolov5':
            # YOLOv5
            try:
                import yolov5
                model = yolov5.load(model_path)
                print("✅ 使用yolov5加载模型成功")
                return model, 'yolov5'
            except ImportError:
                print("❌ 需要安装yolov5: pip install yolov5")
                return None, None
        else:
            print("❌ 未知的模型格式")
            return None, None
            
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return None, None


def run_inference_ultralytics(model, img_dir, output_dir, conf_threshold=0.001):
    """
    使用ultralytics模型进行推理
    """
    os.makedirs(output_dir, exist_ok=True)
    
    # 获取图像文件
    img_files = []
    for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
        img_files.extend(Path(img_dir).glob(f'*{ext}'))
        img_files.extend(Path(img_dir).glob(f'*{ext.upper()}'))
    
    print(f"找到 {len(img_files)} 张图像")
    
    for img_file in img_files:
        try:
            # 推理
            results = model(str(img_file), conf=conf_threshold, verbose=False)
            
            # 获取检测结果
            result = results[0]
            boxes = result.boxes
            
            # 保存YOLO格式标签
            img_name = img_file.stem
            label_file = os.path.join(output_dir, f"{img_name}.txt")
            
            with open(label_file, 'w') as f:
                if boxes is not None and len(boxes) > 0:
                    for box in boxes:
                        # 获取边界框信息
                        cls = int(box.cls.cpu().numpy())
                        conf = float(box.conf.cpu().numpy())
                        xyxy = box.xyxy.cpu().numpy()[0]
                        
                        # 获取图像尺寸
                        img_h, img_w = result.orig_shape
                        
                        # 转换为YOLO格式 (归一化的中心点坐标和宽高)
                        x1, y1, x2, y2 = xyxy
                        x_center = (x1 + x2) / 2 / img_w
                        y_center = (y1 + y2) / 2 / img_h
                        width = (x2 - x1) / img_w
                        height = (y2 - y1) / img_h
                        
                        # 写入文件
                        f.write(f"{cls} {conf:.6f} {x_center:.6f} {y_center:.6f} {width:.6f} {height:.6f}\n")
            
            if len(img_files) <= 10 or len(img_files) % 100 == 0:
                print(f"已处理: {img_name}")
                
        except Exception as e:
            print(f"处理 {img_file} 时出错: {e}")
    
    print(f"✅ 推理完成，结果保存到: {output_dir}")


def run_inference_yolov5(model, img_dir, output_dir, conf_threshold=0.001):
    """
    使用YOLOv5模型进行推理
    """
    os.makedirs(output_dir, exist_ok=True)
    
    # 获取图像文件
    img_files = []
    for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
        img_files.extend(Path(img_dir).glob(f'*{ext}'))
        img_files.extend(Path(img_dir).glob(f'*{ext.upper()}'))
    
    print(f"找到 {len(img_files)} 张图像")
    
    for img_file in img_files:
        try:
            # 推理
            results = model(str(img_file))
            
            # 获取检测结果
            detections = results.pandas().xyxy[0]
            
            # 获取图像尺寸
            img = cv2.imread(str(img_file))
            img_h, img_w = img.shape[:2]
            
            # 保存YOLO格式标签
            img_name = img_file.stem
            label_file = os.path.join(output_dir, f"{img_name}.txt")
            
            with open(label_file, 'w') as f:
                for _, detection in detections.iterrows():
                    if detection['confidence'] >= conf_threshold:
                        cls = int(detection['class'])
                        conf = detection['confidence']
                        x1, y1, x2, y2 = detection['xmin'], detection['ymin'], detection['xmax'], detection['ymax']
                        
                        # 转换为YOLO格式
                        x_center = (x1 + x2) / 2 / img_w
                        y_center = (y1 + y2) / 2 / img_h
                        width = (x2 - x1) / img_w
                        height = (y2 - y1) / img_h
                        
                        f.write(f"{cls} {conf:.6f} {x_center:.6f} {y_center:.6f} {width:.6f} {height:.6f}\n")
            
            if len(img_files) <= 10 or len(img_files) % 100 == 0:
                print(f"已处理: {img_name}")
                
        except Exception as e:
            print(f"处理 {img_file} 时出错: {e}")
    
    print(f"✅ 推理完成，结果保存到: {output_dir}")


def main_pipeline(model_path, img_dir, gt_dir, output_dir='./predictions', conf_threshold=0.001):
    """
    完整的推理和评估流程
    
    Args:
        model_path: 模型文件路径 (best.pt)
        img_dir: 测试图像目录
        gt_dir: GT标签目录 (YOLO格式)
        output_dir: 预测结果输出目录
        conf_threshold: 置信度阈值
    """
    print("🚀 开始完整的推理和评估流程")
    print("=" * 60)
    
    # 1. 检查输入
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return
    
    if not os.path.exists(img_dir):
        print(f"❌ 图像目录不存在: {img_dir}")
        return
    
    if not os.path.exists(gt_dir):
        print(f"❌ GT标签目录不存在: {gt_dir}")
        return
    
    # 2. 加载模型
    print("📦 加载模型...")
    model, model_type = load_yolo_model(model_path)
    if model is None:
        return
    
    # 3. 运行推理
    print("🔍 运行推理...")
    pred_dir = os.path.join(output_dir, 'pred')
    
    if model_type == 'ultralytics':
        run_inference_ultralytics(model, img_dir, pred_dir, conf_threshold)
    elif model_type == 'yolov5':
        run_inference_yolov5(model, img_dir, pred_dir, conf_threshold)
    else:
        print("❌ 不支持的模型类型")
        return
    
    # 4. 运行评估
    print("\n📊 运行评估...")
    
    # 检查预测结果
    pred_files = list(Path(pred_dir).glob('*.txt'))
    if len(pred_files) == 0:
        print("❌ 没有生成预测结果")
        return
    
    print(f"生成了 {len(pred_files)} 个预测文件")
    
    # 创建评估器
    evaluator = YOLOEval(
        gt_dir=gt_dir,
        pred_dir=pred_dir,
        img_dir=img_dir,
        conf_threshold=0.001  # 评估时使用更低的阈值
    )
    
    # IoU评估
    print("\n📈 IoU指标评估...")
    evaluator.evaluate(metric='iou')
    evaluator.accumulate(metric='iou')
    iou_stats = evaluator.summarize(metric='iou')
    
    # SAFit评估
    print("\n🎯 SAFit指标评估...")
    evaluator.evaluate(metric='safit')
    evaluator.accumulate(metric='safit')
    safit_stats = evaluator.summarize(metric='safit')
    
    # 保存结果
    results_dir = os.path.join(output_dir, 'results')
    os.makedirs(results_dir, exist_ok=True)
    
    evaluator.save_results(os.path.join(results_dir, 'iou_results.json'), metric='iou')
    evaluator.save_results(os.path.join(results_dir, 'safit_results.json'), metric='safit')
    
    # 输出总结
    print("\n" + "="*60)
    print("🎉 评估完成！关键指标总结:")
    print("="*60)
    print(f"IoU AP@[0.5:0.95]:     {iou_stats[0]:.3f}")
    print(f"SAFit AP@[0.5:0.95]:   {safit_stats[0]:.3f}")
    print(f"IoU AP@0.5:            {iou_stats[1]:.3f}")
    print(f"SAFit AP@0.5:          {safit_stats[1]:.3f}")
    print(f"IoU AP_tiny:           {iou_stats[4]:.3f}")
    print(f"SAFit AP_tiny:         {safit_stats[4]:.3f}")
    print(f"\n📁 预测结果: {pred_dir}")
    print(f"📁 评估结果: {results_dir}")
    
    return iou_stats, safit_stats


if __name__ == '__main__':
    # 使用示例
    model_path = './best.pt'              # 您的模型文件
    img_dir = './data/images'             # 测试图像目录
    gt_dir = './data/labels/gt'           # GT标签目录
    output_dir = './evaluation_results'   # 输出目录
    
    # 运行完整流程
    main_pipeline(
        model_path=model_path,
        img_dir=img_dir,
        gt_dir=gt_dir,
        output_dir=output_dir,
        conf_threshold=0.001
    )
