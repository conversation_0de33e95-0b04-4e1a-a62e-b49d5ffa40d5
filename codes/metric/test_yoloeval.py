#!/usr/bin/env python3
"""
YOLOEval测试脚本
创建示例数据并测试评估功能
"""

import os
import numpy as np
import cv2
from yoloeval import Y<PERSON><PERSON>val


def create_test_data(base_dir='./test_data'):
    """
    创建测试数据
    """
    # 创建目录
    img_dir = os.path.join(base_dir, 'images')
    gt_dir = os.path.join(base_dir, 'labels', 'gt')
    pred_dir = os.path.join(base_dir, 'labels', 'pred')
    
    os.makedirs(img_dir, exist_ok=True)
    os.makedirs(gt_dir, exist_ok=True)
    os.makedirs(pred_dir, exist_ok=True)
    
    # 创建测试图像和标签
    img_size = (640, 640)
    num_images = 10
    
    for i in range(num_images):
        img_name = f'test_{i:03d}'
        
        # 创建测试图像
        img = np.random.randint(0, 255, (*img_size, 3), dtype=np.uint8)
        cv2.imwrite(os.path.join(img_dir, f'{img_name}.jpg'), img)
        
        # 创建GT标签
        gt_labels = []
        num_objects = np.random.randint(1, 6)  # 1-5个目标
        
        for j in range(num_objects):
            class_id = np.random.randint(0, 3)  # 3个类别
            
            # 随机生成不同尺寸的目标
            if np.random.random() < 0.3:  # 30%概率生成小目标
                w = np.random.uniform(0.01, 0.05)  # 小目标
                h = np.random.uniform(0.01, 0.05)
            else:
                w = np.random.uniform(0.05, 0.3)   # 正常目标
                h = np.random.uniform(0.05, 0.3)
            
            x = np.random.uniform(w/2, 1-w/2)
            y = np.random.uniform(h/2, 1-h/2)
            
            gt_labels.append(f'{class_id} {x:.6f} {y:.6f} {w:.6f} {h:.6f}')
        
        # 保存GT标签
        with open(os.path.join(gt_dir, f'{img_name}.txt'), 'w') as f:
            f.write('\n'.join(gt_labels))
        
        # 创建预测标签（添加一些噪声和漏检）
        pred_labels = []
        
        for label in gt_labels:
            parts = label.split()
            class_id = int(parts[0])
            x, y, w, h = map(float, parts[1:])
            
            # 80%概率检测到目标
            if np.random.random() < 0.8:
                # 添加位置噪声
                x += np.random.normal(0, 0.02)
                y += np.random.normal(0, 0.02)
                w += np.random.normal(0, 0.01)
                h += np.random.normal(0, 0.01)
                
                # 限制在有效范围内
                x = np.clip(x, 0, 1)
                y = np.clip(y, 0, 1)
                w = np.clip(w, 0.01, 1)
                h = np.clip(h, 0.01, 1)
                
                # 随机置信度
                conf = np.random.uniform(0.5, 0.99)
                
                pred_labels.append(f'{class_id} {conf:.6f} {x:.6f} {y:.6f} {w:.6f} {h:.6f}')
        
        # 添加一些假阳性
        num_fp = np.random.randint(0, 3)
        for _ in range(num_fp):
            class_id = np.random.randint(0, 3)
            x = np.random.uniform(0, 1)
            y = np.random.uniform(0, 1)
            w = np.random.uniform(0.02, 0.1)
            h = np.random.uniform(0.02, 0.1)
            conf = np.random.uniform(0.3, 0.7)
            
            pred_labels.append(f'{class_id} {conf:.6f} {x:.6f} {y:.6f} {w:.6f} {h:.6f}')
        
        # 保存预测标签
        with open(os.path.join(pred_dir, f'{img_name}.txt'), 'w') as f:
            f.write('\n'.join(pred_labels))
    
    print(f'测试数据已创建在: {base_dir}')
    return img_dir, gt_dir, pred_dir


def test_yoloeval():
    """
    测试YOLOEval功能
    """
    print('创建测试数据...')
    img_dir, gt_dir, pred_dir = create_test_data()
    
    print('初始化评估器...')
    evaluator = YOLOEval(
        gt_dir=gt_dir,
        pred_dir=pred_dir,
        img_dir=img_dir,
        class_names=['person', 'car', 'bicycle'],
        conf_threshold=0.001
    )
    
    print('\n=== 测试IoU评估 ===')
    try:
        evaluator.evaluate(metric='iou')
        evaluator.accumulate(metric='iou')
        iou_stats = evaluator.summarize(metric='iou')
        evaluator.save_results('./test_results_iou.json', metric='iou')
        print('✅ IoU评估测试通过')
    except Exception as e:
        print(f'❌ IoU评估测试失败: {e}')
    
    print('\n=== 测试SAFit评估 ===')
    try:
        evaluator.evaluate(metric='safit')
        evaluator.accumulate(metric='safit')
        safit_stats = evaluator.summarize(metric='safit')
        evaluator.save_results('./test_results_safit.json', metric='safit')
        print('✅ SAFit评估测试通过')
    except Exception as e:
        print(f'❌ SAFit评估测试失败: {e}')
    
    print('\n=== 测试完成 ===')
    if 'iou_stats' in locals() and 'safit_stats' in locals():
        print(f'IoU AP@[0.5:0.95]: {iou_stats[0]:.3f}')
        print(f'SAFit AP@[0.5:0.95]: {safit_stats[0]:.3f}')
        print('通常SAFit指标会比IoU指标稍高，特别是对小目标')


def test_individual_functions():
    """
    测试单个函数
    """
    print('\n=== 测试单个函数 ===')
    
    evaluator = YOLOEval('.', '.', '.', conf_threshold=0.5)
    
    # 测试IoU计算
    box1 = [10, 10, 50, 50]  # x1, y1, x2, y2
    box2 = [20, 20, 60, 60]
    iou = evaluator.compute_iou(box1, box2)
    print(f'IoU测试: {iou:.3f}')
    
    # 测试NWD计算
    nwd = evaluator.compute_nwd(box1, box2)
    print(f'NWD测试: {nwd:.3f}')
    
    # 测试SAFit计算
    safit = evaluator.compute_safit(box1, box2)
    print(f'SAFit测试: {safit:.3f}')
    
    print('✅ 单个函数测试通过')


if __name__ == '__main__':
    print('YOLOEval 测试脚本')
    print('=' * 50)
    
    # 测试单个函数
    test_individual_functions()
    
    # 测试完整流程
    test_yoloeval()
    
    print('\n所有测试完成！')
    print('如果看到错误，请检查依赖包是否安装完整：')
    print('pip install numpy opencv-python')
