import numpy as np
import os
import cv2
from collections import defaultdict
import json
import time
import datetime


class YOLOEval:
    """
    YOLO格式标签评估器，支持SAFit和传统IoU指标
    
    YOLO格式说明：
    - 标签文件：每行一个目标，格式为 "class_id x_center y_center width height"
    - 坐标范围：[0, 1]，相对于图像尺寸的归一化坐标
    - 预测文件：每行格式为 "class_id confidence x_center y_center width height"
    """
    
    def __init__(self, gt_dir, pred_dir, img_dir, class_names=None, conf_threshold=0.001):
        """
        初始化评估器
        
        Args:
            gt_dir: 真实标签目录（YOLO格式）
            pred_dir: 预测结果目录（YOLO格式）
            img_dir: 图像目录（用于获取图像尺寸）
            class_names: 类别名称列表
            conf_threshold: 置信度阈值
        """
        self.gt_dir = gt_dir
        self.pred_dir = pred_dir
        self.img_dir = img_dir
        self.class_names = class_names or []
        self.conf_threshold = conf_threshold
        
        # 评估参数
        self.iou_thresholds = np.linspace(0.5, 0.95, 10)  # [0.5:0.05:0.95]
        self.recall_thresholds = np.linspace(0.0, 1.0, 101)  # [0:0.01:1]
        self.max_detections = [1, 10, 100]
        
        # 面积范围定义（像素²）
        self.area_ranges = {
            'all': [0, 1e10],
            'extreme_tiny': [0, 64],      # 0² ~ 8²
            'tiny': [64, 256],            # 8² ~ 16²
            'small': [256, 1024],         # 16² ~ 32²
            'medium': [1024, 9216],       # 32² ~ 96²
            'large': [9216, 1e10]         # 96² ~ ∞
        }
        
        # 结果存储
        self.eval_results = {}
        self.stats = []
        
    def load_yolo_labels(self, label_file, img_width, img_height, is_prediction=False):
        """
        加载YOLO格式标签
        
        Args:
            label_file: 标签文件路径
            img_width: 图像宽度
            img_height: 图像高度
            is_prediction: 是否为预测结果（包含置信度）
            
        Returns:
            list: 标签列表，每个元素为字典
        """
        labels = []
        
        if not os.path.exists(label_file):
            return labels
            
        with open(label_file, 'r') as f:
            lines = f.readlines()
            
        for line in lines:
            parts = line.strip().split()
            if len(parts) < 5:
                continue
                
            if is_prediction:
                if len(parts) < 6:
                    continue
                class_id = int(parts[0])
                confidence = float(parts[1])
                x_center = float(parts[2])
                y_center = float(parts[3])
                width = float(parts[4])
                height = float(parts[5])
                
                # 置信度过滤
                if confidence < self.conf_threshold:
                    continue
            else:
                class_id = int(parts[0])
                confidence = 1.0  # GT标签置信度为1
                x_center = float(parts[1])
                y_center = float(parts[2])
                width = float(parts[3])
                height = float(parts[4])
            
            # 转换为绝对坐标
            x_center_abs = x_center * img_width
            y_center_abs = y_center * img_height
            width_abs = width * img_width
            height_abs = height * img_height
            
            # 转换为xyxy格式
            x1 = x_center_abs - width_abs / 2
            y1 = y_center_abs - height_abs / 2
            x2 = x_center_abs + width_abs / 2
            y2 = y_center_abs + height_abs / 2
            
            # 计算面积
            area = width_abs * height_abs
            
            label = {
                'class_id': class_id,
                'confidence': confidence,
                'bbox': [x1, y1, x2, y2],  # xyxy格式
                'area': area
            }
            
            labels.append(label)
            
        return labels
    
    def get_image_size(self, img_name):
        """获取图像尺寸"""
        img_path = os.path.join(self.img_dir, img_name)
        if os.path.exists(img_path):
            img = cv2.imread(img_path)
            if img is not None:
                return img.shape[1], img.shape[0]  # width, height
        
        # 默认尺寸（如果无法读取图像）
        return 640, 640
    
    def compute_iou(self, box1, box2):
        """计算两个边界框的IoU"""
        x1_1, y1_1, x2_1, y2_1 = box1
        x1_2, y1_2, x2_2, y2_2 = box2
        
        # 计算交集
        x1_inter = max(x1_1, x1_2)
        y1_inter = max(y1_1, y1_2)
        x2_inter = min(x2_1, x2_2)
        y2_inter = min(y2_1, y2_2)
        
        if x2_inter <= x1_inter or y2_inter <= y1_inter:
            return 0.0
        
        inter_area = (x2_inter - x1_inter) * (y2_inter - y1_inter)
        
        # 计算并集
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
        union_area = area1 + area2 - inter_area
        
        return inter_area / union_area if union_area > 0 else 0.0
    
    def compute_nwd(self, box1, box2, C=32):
        """计算归一化Wasserstein距离(NWD)"""
        # 转换为中心点坐标和宽高的一半
        x1_1, y1_1, x2_1, y2_1 = box1
        x1_2, y1_2, x2_2, y2_2 = box2
        
        cx1, cy1 = (x1_1 + x2_1) / 2, (y1_1 + y2_1) / 2
        w1, h1 = (x2_1 - x1_1) / 2, (y2_1 - y1_1) / 2
        
        cx2, cy2 = (x1_2 + x2_2) / 2, (y1_2 + y2_2) / 2
        w2, h2 = (x2_2 - x1_2) / 2, (y2_2 - y1_2) / 2
        
        # 计算欧几里得距离
        distance = np.sqrt((cx1 - cx2)**2 + (cy1 - cy2)**2 + (w1 - w2)**2 + (h1 - h2)**2)
        
        # 计算NWD
        nwd = np.exp(-distance / C)
        return nwd
    
    def sigmoid(self, x, k):
        """Sigmoid函数"""
        return 1 / (1 + np.exp(-x / k))
    
    def compute_safit(self, box1, box2, C=32):
        """计算SAFit指标"""
        # 计算IoU
        iou = self.compute_iou(box1, box2)
        
        # 计算NWD
        nwd = self.compute_nwd(box1, box2, C)
        
        # 计算目标面积
        x1_2, y1_2, x2_2, y2_2 = box2  # GT box
        area = (x2_2 - x1_2) * (y2_2 - y1_2)
        
        # 计算SAFit
        weight = self.sigmoid(np.sqrt(area) - C, C)
        safit = weight * iou + (1 - weight) * nwd
        
        return safit

    def evaluate_image(self, img_name, class_id, area_range, max_det, metric='iou'):
        """
        评估单张图像的检测结果

        Args:
            img_name: 图像名称（不含扩展名）
            class_id: 类别ID
            area_range: 面积范围 [min_area, max_area]
            max_det: 最大检测数量
            metric: 评估指标 ('iou', 'safit')

        Returns:
            dict: 评估结果
        """
        # 获取图像尺寸
        img_width, img_height = self.get_image_size(f"{img_name}.jpg")

        # 加载GT和预测标签
        gt_file = os.path.join(self.gt_dir, f"{img_name}.txt")
        pred_file = os.path.join(self.pred_dir, f"{img_name}.txt")

        gt_labels = self.load_yolo_labels(gt_file, img_width, img_height, is_prediction=False)
        pred_labels = self.load_yolo_labels(pred_file, img_width, img_height, is_prediction=True)

        # 过滤指定类别
        if class_id >= 0:
            gt_labels = [gt for gt in gt_labels if gt['class_id'] == class_id]
            pred_labels = [pred for pred in pred_labels if pred['class_id'] == class_id]

        # 过滤面积范围
        gt_labels = [gt for gt in gt_labels if area_range[0] <= gt['area'] <= area_range[1]]

        # 按置信度排序预测结果
        pred_labels = sorted(pred_labels, key=lambda x: x['confidence'], reverse=True)
        pred_labels = pred_labels[:max_det]  # 限制最大检测数量

        if len(gt_labels) == 0 and len(pred_labels) == 0:
            return None

        # 计算相似度矩阵
        if len(gt_labels) > 0 and len(pred_labels) > 0:
            similarity_matrix = np.zeros((len(pred_labels), len(gt_labels)))

            for i, pred in enumerate(pred_labels):
                for j, gt in enumerate(gt_labels):
                    if metric == 'safit':
                        similarity_matrix[i, j] = self.compute_safit(pred['bbox'], gt['bbox'])
                    else:  # iou
                        similarity_matrix[i, j] = self.compute_iou(pred['bbox'], gt['bbox'])
        else:
            similarity_matrix = np.array([])

        # 匹配检测结果和GT
        num_thresholds = len(self.iou_thresholds)
        num_gt = len(gt_labels)
        num_pred = len(pred_labels)

        gt_matches = np.zeros((num_thresholds, num_gt))
        pred_matches = np.zeros((num_thresholds, num_pred))
        pred_ignore = np.zeros((num_thresholds, num_pred))

        if similarity_matrix.size > 0:
            for t_idx, threshold in enumerate(self.iou_thresholds):
                for p_idx in range(num_pred):
                    # 找到最佳匹配的GT
                    best_gt_idx = -1
                    best_similarity = threshold

                    for g_idx in range(num_gt):
                        # 如果GT已经被匹配，跳过
                        if gt_matches[t_idx, g_idx] > 0:
                            continue

                        # 检查相似度是否超过阈值
                        if similarity_matrix[p_idx, g_idx] >= best_similarity:
                            best_similarity = similarity_matrix[p_idx, g_idx]
                            best_gt_idx = g_idx

                    # 如果找到匹配
                    if best_gt_idx >= 0:
                        pred_matches[t_idx, p_idx] = best_gt_idx + 1  # 1-indexed
                        gt_matches[t_idx, best_gt_idx] = p_idx + 1    # 1-indexed

        return {
            'image_name': img_name,
            'class_id': class_id,
            'area_range': area_range,
            'max_det': max_det,
            'gt_count': num_gt,
            'pred_count': num_pred,
            'pred_scores': [pred['confidence'] for pred in pred_labels],
            'pred_matches': pred_matches,
            'gt_matches': gt_matches,
            'pred_ignore': pred_ignore
        }

    def evaluate(self, metric='iou'):
        """
        执行完整评估

        Args:
            metric: 评估指标 ('iou', 'safit')
        """
        print(f'开始评估，使用指标: {metric}')
        start_time = time.time()

        # 获取所有图像文件
        img_files = []
        for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
            img_files.extend([f for f in os.listdir(self.img_dir) if f.lower().endswith(ext)])

        img_names = [os.path.splitext(f)[0] for f in img_files]

        # 获取所有类别
        all_classes = set()
        for img_name in img_names:
            gt_file = os.path.join(self.gt_dir, f"{img_name}.txt")
            if os.path.exists(gt_file):
                with open(gt_file, 'r') as f:
                    for line in f:
                        parts = line.strip().split()
                        if len(parts) >= 5:
                            all_classes.add(int(parts[0]))

        all_classes = sorted(list(all_classes))
        if not all_classes:
            all_classes = [-1]  # 所有类别

        # 执行评估
        eval_results = []

        for class_id in all_classes:
            for area_name, area_range in self.area_ranges.items():
                for max_det in self.max_detections:
                    for img_name in img_names:
                        result = self.evaluate_image(img_name, class_id, area_range, max_det, metric)
                        if result is not None:
                            eval_results.append(result)

        self.eval_results[metric] = eval_results

        end_time = time.time()
        print(f'评估完成，耗时: {end_time - start_time:.2f}秒')

        return eval_results

    def accumulate(self, metric='iou'):
        """
        累积评估结果并计算精度和召回率

        Args:
            metric: 评估指标 ('iou', 'safit')
        """
        print('正在累积评估结果...')
        start_time = time.time()

        if metric not in self.eval_results:
            print(f'请先运行evaluate(metric="{metric}")')
            return

        eval_results = self.eval_results[metric]

        # 组织结果
        results_by_class_area_maxdet = defaultdict(list)

        for result in eval_results:
            key = (result['class_id'], tuple(result['area_range']), result['max_det'])
            results_by_class_area_maxdet[key].append(result)

        # 计算精度和召回率
        num_thresholds = len(self.iou_thresholds)
        num_recall_thresholds = len(self.recall_thresholds)
        num_classes = len(set([r['class_id'] for r in eval_results]))
        num_area_ranges = len(self.area_ranges)
        num_max_dets = len(self.max_detections)

        precision = -np.ones((num_thresholds, num_recall_thresholds, num_classes, num_area_ranges, num_max_dets))
        recall = -np.ones((num_thresholds, num_classes, num_area_ranges, num_max_dets))

        class_ids = sorted(set([r['class_id'] for r in eval_results]))
        area_names = list(self.area_ranges.keys())

        for c_idx, class_id in enumerate(class_ids):
            for a_idx, (area_name, area_range) in enumerate(self.area_ranges.items()):
                for m_idx, max_det in enumerate(self.max_detections):
                    key = (class_id, tuple(area_range), max_det)

                    if key not in results_by_class_area_maxdet:
                        continue

                    results = results_by_class_area_maxdet[key]

                    # 合并所有图像的结果
                    all_pred_scores = []
                    all_pred_matches = []
                    all_gt_count = 0

                    for result in results:
                        all_pred_scores.extend(result['pred_scores'])
                        if len(result['pred_matches']) > 0:
                            all_pred_matches.append(result['pred_matches'])
                        all_gt_count += result['gt_count']

                    if len(all_pred_scores) == 0 or all_gt_count == 0:
                        continue

                    # 按置信度排序
                    sorted_indices = np.argsort(all_pred_scores)[::-1]
                    all_pred_scores = np.array(all_pred_scores)[sorted_indices]

                    # 合并匹配结果
                    if all_pred_matches:
                        all_pred_matches = np.concatenate(all_pred_matches, axis=1)[:, sorted_indices]
                    else:
                        all_pred_matches = np.zeros((num_thresholds, 0))

                    # 计算每个IoU阈值下的精度和召回率
                    for t_idx in range(num_thresholds):
                        if all_pred_matches.shape[1] == 0:
                            continue

                        matches = all_pred_matches[t_idx, :]

                        # 计算TP和FP
                        tp = (matches > 0).astype(float)
                        fp = (matches == 0).astype(float)

                        # 累积TP和FP
                        tp_cumsum = np.cumsum(tp)
                        fp_cumsum = np.cumsum(fp)

                        # 计算精度和召回率
                        recalls = tp_cumsum / all_gt_count
                        precisions = tp_cumsum / (tp_cumsum + fp_cumsum + np.finfo(float).eps)

                        # 记录最大召回率
                        if len(recalls) > 0:
                            recall[t_idx, c_idx, a_idx, m_idx] = recalls[-1]

                        # 平滑精度曲线
                        for i in range(len(precisions) - 2, -1, -1):
                            precisions[i] = max(precisions[i], precisions[i + 1])

                        # 在指定召回率点插值精度
                        precision_at_recalls = np.interp(self.recall_thresholds, recalls, precisions, left=0)
                        precision[t_idx, :, c_idx, a_idx, m_idx] = precision_at_recalls

        self.eval_results[f'{metric}_precision'] = precision
        self.eval_results[f'{metric}_recall'] = recall
        self.eval_results[f'{metric}_class_ids'] = class_ids
        self.eval_results[f'{metric}_area_names'] = area_names

        end_time = time.time()
        print(f'累积完成，耗时: {end_time - start_time:.2f}秒')

    def summarize(self, metric='iou'):
        """
        汇总并显示评估结果

        Args:
            metric: 评估指标 ('iou', 'safit')
        """
        if f'{metric}_precision' not in self.eval_results:
            print(f'请先运行accumulate(metric="{metric}")')
            return

        precision = self.eval_results[f'{metric}_precision']
        recall = self.eval_results[f'{metric}_recall']
        area_names = self.eval_results[f'{metric}_area_names']

        def _summarize(ap=1, iou_thr=None, area_rng='all', max_dets=100):
            """计算指定条件下的AP或AR"""

            # 找到对应的索引
            area_idx = area_names.index(area_rng) if area_rng in area_names else 0
            max_det_idx = self.max_detections.index(max_dets) if max_dets in self.max_detections else -1

            if ap == 1:  # Average Precision
                s = precision
                if iou_thr is not None:
                    t_idx = np.where(np.abs(self.iou_thresholds - iou_thr) < 1e-6)[0]
                    if len(t_idx) > 0:
                        s = s[t_idx[0]]
                    else:
                        return -1
                else:
                    s = s  # 所有IoU阈值

                s = s[:, :, :, area_idx, max_det_idx]
            else:  # Average Recall
                s = recall
                if iou_thr is not None:
                    t_idx = np.where(np.abs(self.iou_thresholds - iou_thr) < 1e-6)[0]
                    if len(t_idx) > 0:
                        s = s[t_idx[0]]
                    else:
                        return -1
                else:
                    s = s  # 所有IoU阈值

                s = s[:, :, area_idx, max_det_idx]

            if len(s[s > -1]) == 0:
                mean_s = -1
            else:
                mean_s = np.mean(s[s > -1])

            return mean_s

        # 计算各种指标
        stats = []

        # AP指标
        stats.append(_summarize(1))  # AP@[0.5:0.95]
        stats.append(_summarize(1, iou_thr=0.5, max_dets=self.max_detections[-1]))  # AP@0.5
        stats.append(_summarize(1, iou_thr=0.75, max_dets=self.max_detections[-1]))  # AP@0.75
        stats.append(_summarize(1, area_rng='extreme_tiny', max_dets=self.max_detections[-1]))  # AP_extreme_tiny
        stats.append(_summarize(1, area_rng='tiny', max_dets=self.max_detections[-1]))  # AP_tiny
        stats.append(_summarize(1, area_rng='small', max_dets=self.max_detections[-1]))  # AP_small
        stats.append(_summarize(1, area_rng='medium', max_dets=self.max_detections[-1]))  # AP_medium
        stats.append(_summarize(1, area_rng='large', max_dets=self.max_detections[-1]))  # AP_large

        # AR指标
        stats.append(_summarize(0, max_dets=self.max_detections[0]))  # AR@1
        stats.append(_summarize(0, max_dets=self.max_detections[1]))  # AR@10
        stats.append(_summarize(0, max_dets=self.max_detections[2]))  # AR@100
        stats.append(_summarize(0, area_rng='extreme_tiny', max_dets=self.max_detections[-1]))  # AR_extreme_tiny
        stats.append(_summarize(0, area_rng='tiny', max_dets=self.max_detections[-1]))  # AR_tiny
        stats.append(_summarize(0, area_rng='small', max_dets=self.max_detections[-1]))  # AR_small
        stats.append(_summarize(0, area_rng='medium', max_dets=self.max_detections[-1]))  # AR_medium
        stats.append(_summarize(0, area_rng='large', max_dets=self.max_detections[-1]))  # AR_large

        self.stats = stats

        # 打印结果
        metric_name = metric.upper()
        print(f'\n{metric_name} 评估结果:')
        print('=' * 80)

        metric_names = [
            f'{metric_name}@[0.5:0.95] | all | 100',
            f'{metric_name}@0.5      | all | 100',
            f'{metric_name}@0.75     | all | 100',
            f'{metric_name}@[0.5:0.95] | extreme_tiny | 100',
            f'{metric_name}@[0.5:0.95] | tiny | 100',
            f'{metric_name}@[0.5:0.95] | small | 100',
            f'{metric_name}@[0.5:0.95] | medium | 100',
            f'{metric_name}@[0.5:0.95] | large | 100',
            f'AR@1         | all | 1',
            f'AR@10        | all | 10',
            f'AR@100       | all | 100',
            f'AR@[0.5:0.95] | extreme_tiny | 100',
            f'AR@[0.5:0.95] | tiny | 100',
            f'AR@[0.5:0.95] | small | 100',
            f'AR@[0.5:0.95] | medium | 100',
            f'AR@[0.5:0.95] | large | 100'
        ]

        for i, (name, value) in enumerate(zip(metric_names, stats)):
            print(f'{name:<40} = {value:.3f}')

        return stats

    def save_results(self, output_file, metric='iou'):
        """
        保存评估结果到JSON文件

        Args:
            output_file: 输出文件路径
            metric: 评估指标
        """
        if not self.stats:
            print('请先运行summarize()方法')
            return

        results = {
            'metric': metric,
            'timestamp': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'parameters': {
                'iou_thresholds': self.iou_thresholds.tolist(),
                'recall_thresholds': self.recall_thresholds.tolist(),
                'max_detections': self.max_detections,
                'area_ranges': self.area_ranges,
                'conf_threshold': self.conf_threshold
            },
            'stats': self.stats
        }

        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2)

        print(f'结果已保存到: {output_file}')


def main():
    """
    使用示例
    """
    # 配置路径
    gt_dir = './labels/gt'          # GT标签目录
    pred_dir = './labels/pred'      # 预测结果目录
    img_dir = './images'            # 图像目录

    # 类别名称（可选）
    class_names = ['person', 'car', 'bicycle']  # 根据实际情况修改

    # 创建评估器
    evaluator = YOLOEval(
        gt_dir=gt_dir,
        pred_dir=pred_dir,
        img_dir=img_dir,
        class_names=class_names,
        conf_threshold=0.001
    )

    # 使用IoU指标评估
    print("使用IoU指标评估...")
    evaluator.evaluate(metric='iou')
    evaluator.accumulate(metric='iou')
    iou_stats = evaluator.summarize(metric='iou')
    evaluator.save_results('./results_iou.json', metric='iou')

    # 使用SAFit指标评估
    print("\n使用SAFit指标评估...")
    evaluator.evaluate(metric='safit')
    evaluator.accumulate(metric='safit')
    safit_stats = evaluator.summarize(metric='safit')
    evaluator.save_results('./results_safit.json', metric='safit')

    print("\n评估完成！")
    print(f"IoU AP@[0.5:0.95]: {iou_stats[0]:.3f}")
    print(f"SAFit AP@[0.5:0.95]: {safit_stats[0]:.3f}")


if __name__ == '__main__':
    main()
