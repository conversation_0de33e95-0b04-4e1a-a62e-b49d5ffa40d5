# SAFit评估工具包 (cp文件夹)

## 📦 **文件清单**

### **主要工具**
- `universal_safit_evaluator.py` - 通用SAFit评估器，支持任何算法的best.pt权重
- `yoloeval.py` - YOLO格式评估器核心模块
- `config_template.yaml` - 配置文件模板

### **完整代码库**
- `codes/` - RGBT-Tiny完整评估代码库
  - `codes/metric/yoloeval.py` - YOLO评估器
  - `codes/metric/cocoeval.py` - COCO评估器  
  - `codes/metric/test_yoloeval.py` - 测试脚本
  - `codes/loss/loss.py` - SAFit损失函数
  - `codes/loss/utils.py` - 工具函数
  - `codes/auxiliary_codes/` - 数据处理辅助工具

### **文档和测试**
- `test_integration.py` - 集成测试脚本
- `UNIVERSAL_USAGE_GUIDE.md` - 详细使用指南
- `README.md` - 本文件

## 🚀 **快速使用**

### **步骤1: 复制到算法目录**
```bash
# 将整个cp文件夹复制到您的算法项目中
cp -r cp/* /path/to/your/algorithm/
```

### **步骤2: 修改配置**
编辑 `config_template.yaml` 为 `config.yaml`:
```yaml
model_path: './runs/train/exp/weights/best.pt'  # 您的模型路径
test_images_dir: './data/test/images'           # 测试图像
gt_labels_dir: './data/test/labels'             # GT标签
class_names: ['person', 'car', 'bicycle']      # 类别名称
dual_modal: false                               # 单模态/双模态
```

### **步骤3: 运行评估**
```bash
# 测试集成
python test_integration.py

# 执行评估
python universal_safit_evaluator.py --config config.yaml
```

## 📋 **数据格式要求**

### **YOLO格式标签**
- **GT标签**: `class_id x_center y_center width height`
- **预测结果**: `class_id confidence x_center y_center width height`
- **坐标范围**: [0, 1] 归一化坐标

### **目录结构**
```
your_algorithm/
├── universal_safit_evaluator.py
├── config.yaml
├── codes/
├── best.pt                    # 训练好的模型
└── data/
    └── test/
        ├── images/           # 测试图像
        └── labels/           # GT标签
```

## 🎯 **支持的算法**

- ✅ **YOLOv5/YOLOv8** - 直接支持
- ✅ **MMDetection** - 自动转换格式
- ✅ **自定义PyTorch** - 通用模型加载
- ✅ **双模态RGB-T** - 支持热红外图像

## 📊 **输出结果**

```
📈 SAFit评估结果
================================================================================
SAFit AP@[0.5:0.95]:  0.489
SAFit AP@0.5:        0.678
SAFit AP_tiny:       0.378  # 小目标性能
SAFit AP_small:      0.456

IoU AP@[0.5:0.95]:    0.423
IoU AP_tiny:         0.291

🎯 SAFit相比IoU提升: +15.6%
```

## ⚠️ **注意事项**

1. **Python依赖**: 需要 torch, opencv-python, numpy, yaml
2. **CUDA支持**: 建议使用GPU加速推理
3. **内存要求**: 大数据集可能需要调整batch_size
4. **文件对应**: 确保图像和标签文件名对应

## 🔧 **自定义适配**

如果您的模型有特殊需求，可以修改：
- `_convert_to_yolo_format()` - 输出格式转换
- `_load_generic_model()` - 模型加载逻辑
- `_predict_dual_modal()` - 双模态推理

## 📞 **技术支持**

遇到问题时：
1. 运行 `test_integration.py` 诊断
2. 查看 `UNIVERSAL_USAGE_GUIDE.md` 详细说明
3. 检查数据格式和路径设置
4. 根据错误信息调整配置

## 🎉 **使用优势**

- 🚀 **即插即用**: 复制文件即可使用
- 🔧 **自动适配**: 支持多种算法类型
- 📊 **专业评估**: SAFit指标更适合小目标
- 📝 **详细报告**: 自动生成评估报告
- 🔄 **格式转换**: 自动处理不同输出格式

开始使用SAFit评估您的算法吧！🎯
