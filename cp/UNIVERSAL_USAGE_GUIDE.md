# 通用SAFit评估器使用指南

## 🎯 **功能特点**

✅ **支持任何算法的best.pt权重文件**  
✅ **自动检测模型类型** (YOLOv5/v8, MMDetection, 自定义PyTorch)  
✅ **支持双模态(RGB-T)和单模态评估**  
✅ **只需修改配置文件即可使用**  
✅ **自动生成YOLO格式预测结果**  
✅ **同时提供SAFit和IoU评估结果**  

## 🚀 **快速开始**

### **步骤1: 准备文件**
```bash
# 复制评估代码到您的算法目录
cp universal_safit_evaluator.py /path/to/your/algorithm/
cp config_template.yaml /path/to/your/algorithm/config.yaml
cp -r codes/ /path/to/your/algorithm/

cd /path/to/your/algorithm/
```

### **步骤2: 修改配置文件**
编辑 `config.yaml`:

```yaml
# 基础配置
model_path: './runs/train/exp/weights/best.pt'  # 您的模型权重路径
test_images_dir: './data/test/images'           # 测试图像目录
gt_labels_dir: './data/test/labels'             # GT标签目录

# 类别配置（根据您的数据集修改）
class_names:
  - 'person'
  - 'car'
  - 'bicycle'

# 模态配置
dual_modal: false  # 单模态设为false，双模态设为true
```

### **步骤3: 运行评估**
```bash
# 使用配置文件
python universal_safit_evaluator.py --config config.yaml

# 或者直接指定参数
python universal_safit_evaluator.py \
    --model ./best.pt \
    --data ./data/test/images

# 双模态评估
python universal_safit_evaluator.py \
    --config config.yaml \
    --dual-modal
```

## 📁 **目录结构要求**

### **单模态评估**
```
your_algorithm/
├── universal_safit_evaluator.py  # 评估脚本
├── config.yaml                   # 配置文件
├── codes/                         # RGBT-Tiny评估代码
├── best.pt                        # 训练好的模型权重
└── data/
    └── test/
        ├── images/               # 测试图像
        │   ├── img001.jpg
        │   ├── img002.jpg
        │   └── ...
        └── labels/               # GT标签(YOLO格式)
            ├── img001.txt
            ├── img002.txt
            └── ...
```

### **双模态评估**
```
your_algorithm/
├── universal_safit_evaluator.py
├── config.yaml
├── codes/
├── best.pt
└── data/
    └── test/
        ├── images/               # RGB图像
        │   ├── img001.jpg
        │   └── ...
        ├── thermal/              # 热红外图像
        │   ├── img001.jpg
        │   └── ...
        └── labels/               # GT标签
            ├── img001.txt
            └── ...
```

## 🔧 **支持的算法类型**

### **1. YOLOv5/YOLOv8**
```yaml
model_path: './runs/train/exp/weights/best.pt'
model_type: 'yolo'  # 或 'auto'
```

### **2. MMDetection**
```yaml
model_path: './work_dirs/faster_rcnn/latest.pth'
model_type: 'mmdet'
mmdet_config: './configs/faster_rcnn_r50_fpn_1x_coco.py'
```

### **3. 自定义PyTorch模型**
```yaml
model_path: './checkpoints/model_best.pth'
model_type: 'torch'
```

### **4. 双模态RGBT算法**
```yaml
model_path: './rgbt_model_best.pt'
dual_modal: true
thermal_images_dir: './data/test/thermal'
```

## ⚙️ **自定义模型适配**

如果您的模型需要特殊处理，可以修改以下方法：

### **1. 模型架构定义**
在 `_create_model_architecture()` 方法中添加您的模型：

```python
def _create_model_architecture(self):
    # 添加您的模型导入和初始化代码
    from your_model import YourModel
    return YourModel(num_classes=len(self.class_names))
```

### **2. 预测结果转换**
在 `_convert_to_yolo_format()` 方法中添加您的输出格式处理：

```python
def _convert_to_yolo_format(self, predictions, img_width, img_height):
    yolo_preds = []
    
    # 添加您的模型输出格式处理代码
    if isinstance(predictions, YourModelOutput):
        for detection in predictions.detections:
            # 转换逻辑
            pass
    
    return yolo_preds
```

### **3. 双模态推理**
如果您的模型支持双模态输入，修改 `_predict_dual_modal()` 方法：

```python
def _predict_dual_modal(self, rgb_img, thermal_img):
    rgb_tensor = self._preprocess_image(rgb_img)
    thermal_tensor = self._preprocess_image(thermal_img)
    
    with torch.no_grad():
        # 您的双模态推理代码
        predictions = self.model(rgb_tensor, thermal_tensor)
    
    return predictions
```

## 📊 **结果解读**

评估完成后会显示：

```
📈 SAFit评估结果
================================================================================
SAFit AP@[0.5:0.95]:  0.489
SAFit AP@0.5:        0.678
SAFit AP@0.75:       0.423
SAFit AP_tiny:       0.378
SAFit AP_small:      0.456

IoU AP@[0.5:0.95]:    0.423
IoU AP@0.5:          0.651
IoU AP@0.75:         0.398
IoU AP_tiny:         0.291
IoU AP_small:        0.398

🎯 SAFit相比IoU提升: +15.6%
```

**关键指标说明：**
- **SAFit AP@[0.5:0.95]**: 最重要的综合指标，论文主要报告
- **SAFit AP_tiny**: 极小目标性能 (8²~16²像素)
- **SAFit AP_small**: 小目标性能 (16²~32²像素)

## 🔍 **常见问题解决**

### **Q1: 模型加载失败**
```bash
# 检查模型文件是否存在
ls -la ./best.pt

# 检查模型类型
python -c "import torch; print(torch.load('./best.pt', map_location='cpu').keys())"
```

### **Q2: 预测结果转换失败**
在 `_convert_to_yolo_format()` 方法中添加调试信息：
```python
print(f"预测结果类型: {type(predictions)}")
print(f"预测结果内容: {predictions}")
```

### **Q3: 双模态图像未找到**
确保热红外图像路径正确：
```yaml
thermal_images_dir: './data/test/thermal'  # 确保路径存在
```

### **Q4: GT标签格式错误**
确保GT标签是YOLO格式：
```
# 每行: class_id x_center y_center width height
0 0.5 0.5 0.2 0.3
1 0.3 0.7 0.1 0.15
```

## 🎯 **使用建议**

### **1. 论文写作**
```
我们使用RGBT-Tiny数据集提出的SAFit指标评估模型性能。
在SAFit@[0.5:0.95]指标上，我们的方法达到48.9%，
相比传统IoU指标的42.3%提升了6.6个百分点。
```

### **2. 模型对比**
- 使用相同的评估脚本对比不同算法
- 重点关注SAFit_tiny和SAFit_small指标
- 同时报告IoU结果以便与其他工作对比

### **3. 性能优化**
- 如果评估速度慢，可以调整batch_size
- 对于大规模测试，可以使用多GPU并行

## 🚀 **一键使用示例**

```bash
# 1. 克隆或复制文件
git clone your_algorithm_repo
cd your_algorithm

# 2. 复制评估代码
cp /path/to/universal_safit_evaluator.py ./
cp /path/to/config_template.yaml ./config.yaml

# 3. 修改配置
vim config.yaml  # 修改模型路径和数据路径

# 4. 运行评估
python universal_safit_evaluator.py --config config.yaml

# 5. 查看结果
ls evaluation_output/
```

这样就可以在任何算法上快速使用SAFit评估了！🎉
