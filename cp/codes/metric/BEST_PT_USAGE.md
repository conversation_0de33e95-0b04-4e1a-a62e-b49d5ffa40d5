# 使用best.pt模型进行评估的完整指南

## 🎯 **快速开始（3步完成）**

### **步骤1：准备文件**
```bash
# 将以下文件复制到您的项目目录
cp yoloeval.py your_project/
cp inference_and_eval.py your_project/
cp quick_eval.py your_project/
```

### **步骤2：安装依赖**
```bash
# 基础依赖
pip install numpy opencv-python torch

# 根据您的YOLO版本安装
pip install ultralytics  # YOLOv8/v10
# 或
pip install yolov5      # YOLOv5
```

### **步骤3：运行评估**
```bash
python quick_eval.py --model best.pt --images ./test_images --labels ./test_labels
```

## 📁 **目录结构要求**

```
your_project/
├── best.pt                    # 您的训练好的模型
├── yoloeval.py               # 评估工具
├── inference_and_eval.py     # 推理和评估脚本
├── quick_eval.py             # 快速评估脚本
├── test_images/              # 测试图像
│   ├── img001.jpg
│   ├── img002.jpg
│   └── ...
├── test_labels/              # GT标签（YOLO格式）
│   ├── img001.txt
│   ├── img002.txt
│   └── ...
└── eval_output/              # 评估结果（自动创建）
    ├── pred/                 # 模型预测结果
    └── results/              # 评估指标结果
```

## 🚀 **使用方法**

### **方法1：命令行快速评估**
```bash
# 基本用法
python quick_eval.py --model best.pt --images ./test_images --labels ./test_labels

# 自定义输出目录和置信度
python quick_eval.py \
    --model ./models/best.pt \
    --images ./data/test/images \
    --labels ./data/test/labels \
    --output ./my_results \
    --conf 0.25
```

### **方法2：Python脚本调用**
```python
from inference_and_eval import main_pipeline

# 运行评估
results = main_pipeline(
    model_path='./best.pt',
    img_dir='./test_images',
    gt_dir='./test_labels',
    output_dir='./eval_results',
    conf_threshold=0.001
)

if results:
    iou_stats, safit_stats = results
    print(f"IoU AP: {iou_stats[0]:.3f}")
    print(f"SAFit AP: {safit_stats[0]:.3f}")
```

### **方法3：集成到训练脚本**
```python
# 在您的train.py末尾添加
if __name__ == '__main__':
    # ... 训练代码 ...
    
    # 训练完成后自动评估
    print("训练完成，开始评估...")
    from inference_and_eval import main_pipeline
    
    main_pipeline(
        model_path='./runs/train/exp/weights/best.pt',
        img_dir='./data/test/images',
        gt_dir='./data/test/labels',
        output_dir='./evaluation'
    )
```

## 📊 **输出结果解读**

### **控制台输出示例**
```
🚀 开始完整的推理和评估流程
============================================================
📦 加载模型...
检测到模型版本: ultralytics
✅ 使用ultralytics加载模型成功

🔍 运行推理...
找到 1000 张图像
已处理: img001
已处理: img002
...
✅ 推理完成，结果保存到: ./eval_output/pred

📊 运行评估...
生成了 1000 个预测文件

📈 IoU指标评估...
IoU 评估结果:
================================================================================
IoU@[0.5:0.95] | all | 100              = 0.456
IoU@0.5        | all | 100              = 0.678
IoU@0.75       | all | 100              = 0.512
IoU@[0.5:0.95] | tiny | 100             = 0.234
...

🎯 SAFit指标评估...
SAFIT 评估结果:
================================================================================
SAFIT@[0.5:0.95] | all | 100            = 0.489
SAFIT@0.5        | all | 100            = 0.701
SAFIT@0.75       | all | 100            = 0.534
SAFIT@[0.5:0.95] | tiny | 100           = 0.267
...

============================================================
🎉 评估完成！关键指标总结:
============================================================
IoU AP@[0.5:0.95]:     0.456
SAFit AP@[0.5:0.95]:   0.489
IoU AP@0.5:            0.678
SAFit AP@0.5:          0.701
IoU AP_tiny:           0.234
SAFit AP_tiny:         0.267

📁 预测结果: ./eval_output/pred
📁 评估结果: ./eval_output/results
```

### **生成的文件**
```
eval_output/
├── pred/                     # 模型预测结果（YOLO格式）
│   ├── img001.txt           # class_id conf x_center y_center width height
│   ├── img002.txt
│   └── ...
└── results/                  # 评估结果
    ├── iou_results.json     # IoU指标详细结果
    └── safit_results.json   # SAFit指标详细结果
```

## 🔧 **支持的模型格式**

### **自动检测支持**
- ✅ **YOLOv5**: 需要 `pip install yolov5`
- ✅ **YOLOv8**: 需要 `pip install ultralytics`
- ✅ **YOLOv10**: 需要 `pip install ultralytics`
- ✅ **其他ultralytics格式模型**

### **模型加载示例**
```python
# 脚本会自动检测并加载
检测到模型版本: ultralytics
✅ 使用ultralytics加载模型成功
```

## 📝 **数据格式要求**

### **GT标签格式** (test_labels/img001.txt)
```
# 每行：class_id x_center y_center width height
0 0.5 0.5 0.2 0.3
1 0.3 0.7 0.1 0.15
```

### **生成的预测格式** (eval_output/pred/img001.txt)
```
# 每行：class_id confidence x_center y_center width height
0 0.95 0.51 0.49 0.19 0.31
1 0.87 0.29 0.71 0.11 0.14
```

## ⚠️ **常见问题解决**

### **问题1：模型加载失败**
```bash
❌ 模型加载失败: No module named 'ultralytics'

# 解决方案
pip install ultralytics
```

### **问题2：找不到图像**
```bash
❌ 图像目录不存在: ./test_images

# 解决方案
mkdir test_images
# 将测试图像放入该目录
```

### **问题3：标签格式错误**
```bash
# 检查标签格式
head test_labels/img001.txt
# 应该看到：0 0.5 0.5 0.2 0.3

# 如果是其他格式，需要转换为YOLO格式
```

### **问题4：没有检测结果**
```bash
# 降低置信度阈值
python quick_eval.py --model best.pt --images ./test_images --labels ./test_labels --conf 0.001
```

## 🎯 **论文写作建议**

### **实验设置**
```
我们使用训练好的模型在测试集上进行推理，然后采用RGBT-Tiny数据集
提出的SAFit评估协议。SAFit指标根据目标尺寸自适应地融合IoU和
归一化Wasserstein距离，对小目标检测提供更准确的评估。
```

### **结果报告**
```
表X显示了我们方法的性能。在SAFit@[0.5:0.95]指标上，我们的方法
达到了48.9%，相比基线方法提升了6.6个百分点。特别是在小目标
检测上，SAFit_tiny指标达到26.7%，显著优于传统IoU_tiny的23.4%。
```

## 🚀 **一键运行命令**

```bash
# 1. 复制文件
cp yoloeval.py inference_and_eval.py quick_eval.py ./

# 2. 安装依赖
pip install ultralytics numpy opencv-python

# 3. 运行评估
python quick_eval.py --model best.pt --images ./test_images --labels ./test_labels

# 4. 查看结果
cat eval_output/results/safit_results.json
```

## 📞 **技术支持**

如果遇到问题：
1. 确认模型文件`best.pt`存在且可加载
2. 确认测试图像和标签目录存在
3. 确认标签格式为YOLO格式（归一化坐标）
4. 检查依赖包是否正确安装

祝您评估顺利！🎉
