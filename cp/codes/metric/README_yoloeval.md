# YOLOEval - YOLO格式标签评估工具

## 📋 功能概述

`yoloeval.py` 是一个专门为YOLO格式标签设计的评估工具，支持传统IoU指标和RGBT-Tiny提出的SAFit指标。

## 🎯 支持的评估指标

### 1. **传统IoU指标**
- **AP@[0.5:0.95]**: 在IoU阈值0.5到0.95范围内的平均精度
- **AP@0.5**: IoU阈值为0.5时的平均精度  
- **AP@0.75**: IoU阈值为0.75时的平均精度

### 2. **SAFit指标（推荐用于小目标）**
- **SAFit@[0.5:0.95]**: 使用SAFit指标的平均精度
- **SAFit@0.5**: SAFit阈值为0.5时的平均精度
- **SAFit@0.75**: SAFit阈值为0.75时的平均精度

### 3. **不同尺度目标的指标**
- **AP_extreme_tiny**: 极小目标 (0² ~ 8²像素)
- **AP_tiny**: 微小目标 (8² ~ 16²像素)  
- **AP_small**: 小目标 (16² ~ 32²像素)
- **AP_medium**: 中等目标 (32² ~ 96²像素)
- **AP_large**: 大目标 (96²像素以上)

### 4. **召回率指标**
- **AR@1**: 每张图像最多1个检测的平均召回率
- **AR@10**: 每张图像最多10个检测的平均召回率
- **AR@100**: 每张图像最多100个检测的平均召回率

## 📁 文件格式要求

### YOLO标签格式

#### GT标签文件 (ground truth)
```
# 每行格式：class_id x_center y_center width height
0 0.5 0.5 0.2 0.3
1 0.3 0.7 0.1 0.15
```

#### 预测结果文件 (predictions)  
```
# 每行格式：class_id confidence x_center y_center width height
0 0.95 0.51 0.49 0.19 0.31
1 0.87 0.29 0.71 0.11 0.14
```

### 目录结构
```
project/
├── images/           # 图像文件
│   ├── img001.jpg
│   ├── img002.jpg
│   └── ...
├── labels/
│   ├── gt/          # GT标签
│   │   ├── img001.txt
│   │   ├── img002.txt
│   │   └── ...
│   └── pred/        # 预测结果
│       ├── img001.txt
│       ├── img002.txt
│       └── ...
```

## 🚀 使用方法

### 基本使用
```python
from yoloeval import YOLOEval

# 创建评估器
evaluator = YOLOEval(
    gt_dir='./labels/gt',
    pred_dir='./labels/pred', 
    img_dir='./images',
    class_names=['person', 'car', 'bicycle'],
    conf_threshold=0.001
)

# IoU评估
evaluator.evaluate(metric='iou')
evaluator.accumulate(metric='iou')
iou_stats = evaluator.summarize(metric='iou')

# SAFit评估  
evaluator.evaluate(metric='safit')
evaluator.accumulate(metric='safit')
safit_stats = evaluator.summarize(metric='safit')

# 保存结果
evaluator.save_results('./results_iou.json', metric='iou')
evaluator.save_results('./results_safit.json', metric='safit')
```

### 命令行使用
```bash
cd codes/metric
python yoloeval.py
```

## 📊 输出结果示例

```
IoU 评估结果:
================================================================================
IoU@[0.5:0.95] | all | 100              = 0.456
IoU@0.5        | all | 100              = 0.678
IoU@0.75       | all | 100              = 0.512
IoU@[0.5:0.95] | extreme_tiny | 100     = 0.234
IoU@[0.5:0.95] | tiny | 100             = 0.345
IoU@[0.5:0.95] | small | 100            = 0.456
IoU@[0.5:0.95] | medium | 100           = 0.567
IoU@[0.5:0.95] | large | 100            = 0.678
AR@1           | all | 1                = 0.123
AR@10          | all | 10               = 0.456
AR@100         | all | 100              = 0.567
...

SAFit 评估结果:
================================================================================
SAFIT@[0.5:0.95] | all | 100            = 0.489
SAFIT@0.5        | all | 100            = 0.701
SAFIT@0.75       | all | 100            = 0.534
SAFIT@[0.5:0.95] | extreme_tiny | 100   = 0.267
SAFIT@[0.5:0.95] | tiny | 100           = 0.378
...
```

## 🔧 参数配置

### 主要参数
- **conf_threshold**: 置信度阈值，默认0.001
- **iou_thresholds**: IoU阈值范围，默认[0.5:0.05:0.95]
- **max_detections**: 最大检测数量，默认[1, 10, 100]
- **area_ranges**: 目标尺度范围定义

### SAFit参数
- **C**: SAFit指标中的尺度参数，默认32

## 📈 论文中的使用

### 方法描述
```
我们使用RGBT-Tiny数据集提供的评估协议，采用SAFit指标评估小目标检测性能。
SAFit指标根据目标尺寸自适应地融合IoU和NWD(Normalized Wasserstein Distance)，
对小目标提供更准确的评估。
```

### 结果报告
```
在RGBT-Tiny数据集上，我们的方法取得了以下结果：
- SAFit@[0.5:0.95]: 48.9%
- SAFit@0.5: 70.1%  
- SAFit_tiny: 37.8%
- SAFit_small: 45.6%
```

## ⚠️ 注意事项

1. **坐标格式**: 确保YOLO标签使用归一化坐标[0,1]
2. **文件对应**: GT和预测文件名必须一致
3. **图像尺寸**: 需要图像文件来获取真实尺寸
4. **类别一致**: GT和预测中的类别ID必须一致

## 🔍 与COCO评估的区别

| 特性 | COCO评估 | YOLOEval |
|------|----------|----------|
| 输入格式 | JSON | YOLO txt |
| 坐标系统 | 绝对坐标 | 归一化坐标 |
| SAFit支持 | ❌ | ✅ |
| 小目标优化 | 基础 | 专门优化 |

## 📚 相关论文引用

```bibtex
@article{RGBT-Tiny,
 title = {Visible-Thermal Tiny Object Detection: A Benchmark Dataset and Baselines},
 author = {Xinyi Ying and Chao Xiao and ...},
 journal = {IEEE Transactions on Pattern Analysis and Machine Intelligence (TPAMI)},
 year = {2025},
}
```
