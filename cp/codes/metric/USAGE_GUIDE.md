# YOLOEval 使用指南 - 集成到其他算法项目

## 📋 **文件复制清单**

需要复制的文件：
```
yoloeval.py          # 主评估工具
test_yoloeval.py     # 测试脚本
README_yoloeval.md   # 使用说明（可选）
```

## 🚀 **快速集成步骤**

### **步骤1：复制文件**
```bash
# 在您的算法项目根目录下
mkdir -p evaluation
cp yoloeval.py evaluation/
cp test_yoloeval.py evaluation/
```

### **步骤2：安装依赖**
```bash
pip install numpy opencv-python
```

### **步骤3：验证安装**
```bash
cd evaluation
python test_yoloeval.py
```

看到 `✅ 测试通过` 说明安装成功。

## 📁 **项目目录结构**

```
your_algorithm_project/
├── models/                    # 您的模型代码
├── train.py                   # 训练脚本
├── inference.py               # 推理脚本
├── evaluation/                # 评估工具目录
│   ├── yoloeval.py           # 评估工具
│   ├── test_yoloeval.py      # 测试脚本
│   └── evaluate_model.py     # 您的评估脚本（新建）
├── data/
│   ├── images/               # 测试图像
│   ├── labels/
│   │   ├── gt/              # 真实标签（YOLO格式）
│   │   └── pred/            # 模型预测结果（YOLO格式）
└── results/                  # 评估结果输出
```

## 🔧 **创建评估脚本**

创建 `evaluation/evaluate_model.py`：

```python
#!/usr/bin/env python3
"""
模型评估脚本
使用YOLOEval工具评估您的算法性能
"""

import os
import sys
from yoloeval import YOLOEval

def evaluate_my_model():
    """
    评估您的模型
    """
    # 配置路径（根据您的项目结构调整）
    base_dir = '../data'  # 相对于evaluation目录
    img_dir = os.path.join(base_dir, 'images')
    gt_dir = os.path.join(base_dir, 'labels', 'gt')
    pred_dir = os.path.join(base_dir, 'labels', 'pred')
    
    # 检查路径是否存在
    for path, name in [(img_dir, '图像'), (gt_dir, 'GT标签'), (pred_dir, '预测结果')]:
        if not os.path.exists(path):
            print(f"❌ {name}目录不存在: {path}")
            return
    
    # 类别名称（根据您的数据集修改）
    class_names = ['person', 'car', 'bicycle']  # 修改为您的类别
    
    print("🚀 开始评估模型...")
    
    # 创建评估器
    evaluator = YOLOEval(
        gt_dir=gt_dir,
        pred_dir=pred_dir,
        img_dir=img_dir,
        class_names=class_names,
        conf_threshold=0.001  # 置信度阈值
    )
    
    # IoU评估
    print("\n📊 使用IoU指标评估...")
    evaluator.evaluate(metric='iou')
    evaluator.accumulate(metric='iou')
    iou_stats = evaluator.summarize(metric='iou')
    
    # SAFit评估（推荐用于论文）
    print("\n🎯 使用SAFit指标评估...")
    evaluator.evaluate(metric='safit')
    evaluator.accumulate(metric='safit')
    safit_stats = evaluator.summarize(metric='safit')
    
    # 保存结果
    results_dir = '../results'
    os.makedirs(results_dir, exist_ok=True)
    
    evaluator.save_results(os.path.join(results_dir, 'iou_results.json'), metric='iou')
    evaluator.save_results(os.path.join(results_dir, 'safit_results.json'), metric='safit')
    
    # 输出关键指标
    print("\n" + "="*60)
    print("📈 关键评估结果:")
    print("="*60)
    print(f"IoU AP@[0.5:0.95]:    {iou_stats[0]:.3f}")
    print(f"SAFit AP@[0.5:0.95]:  {safit_stats[0]:.3f}")
    print(f"IoU AP@0.5:           {iou_stats[1]:.3f}")
    print(f"SAFit AP@0.5:         {safit_stats[1]:.3f}")
    print(f"IoU AP_tiny:          {iou_stats[4]:.3f}")
    print(f"SAFit AP_tiny:        {safit_stats[4]:.3f}")
    
    print(f"\n💾 结果已保存到: {results_dir}")
    print("✅ 评估完成！")
    
    return iou_stats, safit_stats

if __name__ == '__main__':
    evaluate_my_model()
```

## 📝 **数据格式要求**

### **GT标签格式** (`labels/gt/image_name.txt`)
```
# 每行：class_id x_center y_center width height
0 0.5 0.5 0.2 0.3
1 0.3 0.7 0.1 0.15
```

### **预测结果格式** (`labels/pred/image_name.txt`)
```
# 每行：class_id confidence x_center y_center width height
0 0.95 0.51 0.49 0.19 0.31
1 0.87 0.29 0.71 0.11 0.14
```

### **重要说明**
- 坐标必须是归一化的 [0, 1]
- GT和预测文件名必须对应
- 图像文件用于获取真实尺寸

## 🔄 **集成到训练流程**

### **方法1：训练后评估**
```python
# 在您的train.py末尾添加
if __name__ == '__main__':
    # ... 训练代码 ...
    
    # 训练完成后评估
    print("训练完成，开始评估...")
    os.system('cd evaluation && python evaluate_model.py')
```

### **方法2：定期评估**
```python
# 在训练循环中
for epoch in range(num_epochs):
    # ... 训练代码 ...
    
    if epoch % 10 == 0:  # 每10个epoch评估一次
        # 生成预测结果
        generate_predictions()
        
        # 运行评估
        from evaluation.yoloeval import YOLOEval
        evaluator = YOLOEval(gt_dir, pred_dir, img_dir)
        evaluator.evaluate(metric='safit')
        evaluator.accumulate(metric='safit')
        stats = evaluator.summarize(metric='safit')
        
        print(f"Epoch {epoch} SAFit AP: {stats[0]:.3f}")
```

## 📊 **结果解读**

### **主要指标含义**
- **AP@[0.5:0.95]**: 最重要的综合指标
- **AP@0.5**: 宽松评估（IoU>0.5即算正确）
- **AP@0.75**: 严格评估（IoU>0.75才算正确）
- **AP_tiny**: 小目标性能（8²~16²像素）
- **AP_small**: 小目标性能（16²~32²像素）

### **IoU vs SAFit**
- **IoU**: 传统指标，便于与其他工作比较
- **SAFit**: 小目标优化指标，更适合微小目标检测

## 🎯 **论文写作建议**

### **实验设置部分**
```
我们采用RGBT-Tiny数据集提出的评估协议，使用SAFit指标评估检测性能。
SAFit指标根据目标尺寸自适应地融合IoU和归一化Wasserstein距离，
对小目标检测提供更准确的评估。同时我们也报告传统IoU指标以便比较。
```

### **结果报告部分**
```
表X显示了我们方法在测试集上的性能。我们的方法在SAFit@[0.5:0.95]
指标上达到了48.9%，相比基线方法的42.3%提升了6.6个百分点。
特别是在小目标检测上，SAFit_tiny指标达到37.8%，显著优于
传统方法的29.1%。
```

## ⚠️ **常见问题解决**

### **问题1：ImportError**
```bash
# 解决方案
pip install numpy opencv-python
```

### **问题2：路径错误**
```python
# 检查路径是否正确
import os
print("当前目录:", os.getcwd())
print("图像目录存在:", os.path.exists('data/images'))
```

### **问题3：标签格式错误**
```python
# 检查标签格式
with open('data/labels/gt/sample.txt', 'r') as f:
    print("GT标签示例:", f.readline().strip())
```

### **问题4：结果异常**
```bash
# 先运行测试确认代码正常
cd evaluation
python test_yoloeval.py
```

## 🚀 **快速开始命令**

```bash
# 1. 复制文件到您的项目
cp yoloeval.py your_project/evaluation/
cp test_yoloeval.py your_project/evaluation/

# 2. 进入项目目录
cd your_project/evaluation

# 3. 测试工具
python test_yoloeval.py

# 4. 创建评估脚本（复制上面的evaluate_model.py）
nano evaluate_model.py

# 5. 运行评估
python evaluate_model.py
```

## 📞 **技术支持**

如果遇到问题：
1. 先运行 `test_yoloeval.py` 确认基础功能
2. 检查数据格式是否符合YOLO标准
3. 确认文件路径设置正确
4. 查看错误信息定位问题

祝您使用顺利！🎉
