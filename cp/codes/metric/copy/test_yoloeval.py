#!/usr/bin/env python3
"""
YOLOEval测试脚本
创建示例数据并测试评估功能
"""

import os
import numpy as np
import cv2
from yoloeval import YOLOEval
from pathlib import Path

# 尝试导入ultralytics，如果失败则只支持测试模式
try:
    from ultralytics import YOLO
    ULTRALYTICS_AVAILABLE = True
except ImportError:
    ULTRALYTICS_AVAILABLE = False
    print("警告: ultralytics未安装，只能运行测试模式")


def generate_yolov8_predictions(model_path, img_dir, pred_dir, conf_threshold=0.001, iou_threshold=0.6):
    """
    使用YOLOv8模型生成预测结果

    Args:
        model_path: 模型文件路径
        img_dir: 图像目录
        pred_dir: 预测结果输出目录
        conf_threshold: 置信度阈值
        iou_threshold: NMS IoU阈值

    Returns:
        bool: 是否成功
    """
    if not ULTRALYTICS_AVAILABLE:
        print('❌ ultralytics未安装，无法生成预测结果')
        return False

    try:
        # 创建输出目录
        os.makedirs(pred_dir, exist_ok=True)

        # 加载模型
        print(f'📥 加载模型: {model_path}')
        model = YOLO(model_path)

        # 获取所有图像文件
        img_extensions = ['.jpg', '.jpeg', '.png', '.bmp']
        img_files = []

        for ext in img_extensions:
            img_files.extend(Path(img_dir).glob(f'*{ext}'))
            img_files.extend(Path(img_dir).glob(f'*{ext.upper()}'))

        img_files = sorted(img_files)
        print(f'📊 找到 {len(img_files)} 张图像')

        if len(img_files) == 0:
            print('❌ 未找到图像文件')
            return False

        # 批量推理
        print('🔄 开始推理...')
        processed = 0

        for img_file in img_files:
            try:
                # 推理
                results = model.predict(
                    source=str(img_file),
                    conf=conf_threshold,
                    iou=iou_threshold,
                    device='0',
                    imgsz=640,
                    verbose=False
                )

                # 获取图像尺寸
                img_width = results[0].orig_shape[1]
                img_height = results[0].orig_shape[0]

                # 转换预测结果为YOLO格式
                predictions = []

                if results[0].boxes is not None:
                    boxes = results[0].boxes

                    # 获取检测结果
                    xyxy = boxes.xyxy.cpu().numpy()  # 边界框坐标 (x1, y1, x2, y2)
                    conf = boxes.conf.cpu().numpy()  # 置信度
                    cls = boxes.cls.cpu().numpy()    # 类别

                    for i in range(len(xyxy)):
                        if conf[i] >= conf_threshold:
                            x1, y1, x2, y2 = xyxy[i]

                            # 转换为YOLO格式 (归一化的中心点坐标和宽高)
                            x_center = (x1 + x2) / 2 / img_width
                            y_center = (y1 + y2) / 2 / img_height
                            width = (x2 - x1) / img_width
                            height = (y2 - y1) / img_height

                            # 确保坐标在有效范围内
                            x_center = max(0, min(1, x_center))
                            y_center = max(0, min(1, y_center))
                            width = max(0, min(1, width))
                            height = max(0, min(1, height))

                            # YOLOv5预测格式: class_id x_center y_center width height confidence
                            predictions.append(f"{int(cls[i])} {x_center:.6f} {y_center:.6f} {width:.6f} {height:.6f} {conf[i]:.6f}")

                # 保存预测结果
                output_file = os.path.join(pred_dir, f"{img_file.stem}.txt")
                with open(output_file, 'w') as f:
                    if predictions:
                        f.write('\n'.join(predictions))
                    # 如果没有预测结果，创建空文件

                processed += 1
                if processed % 100 == 0:
                    print(f'  已处理: {processed}/{len(img_files)}')

            except Exception as e:
                print(f'处理 {img_file} 时出错: {e}')
                continue

        print(f'✅ 预测生成完成! 处理了 {processed}/{len(img_files)} 张图像')
        return True

    except Exception as e:
        print(f'❌ 预测生成失败: {e}')
        return False


def create_test_data(base_dir='./test_data'):
    """
    创建测试数据
    """
    # 创建目录
    img_dir = os.path.join(base_dir, 'images')
    gt_dir = os.path.join(base_dir, 'labels', 'gt')
    pred_dir = os.path.join(base_dir, 'labels', 'pred')
    
    os.makedirs(img_dir, exist_ok=True)
    os.makedirs(gt_dir, exist_ok=True)
    os.makedirs(pred_dir, exist_ok=True)
    
    # 创建测试图像和标签
    img_size = (640, 640)
    num_images = 10
    
    for i in range(num_images):
        img_name = f'test_{i:03d}'
        
        # 创建测试图像
        img = np.random.randint(0, 255, (*img_size, 3), dtype=np.uint8)
        cv2.imwrite(os.path.join(img_dir, f'{img_name}.jpg'), img)
        
        # 创建GT标签
        gt_labels = []
        num_objects = np.random.randint(1, 6)  # 1-5个目标
        
        for j in range(num_objects):
            class_id = np.random.randint(0, 3)  # 3个类别
            
            # 随机生成不同尺寸的目标
            if np.random.random() < 0.3:  # 30%概率生成小目标
                w = np.random.uniform(0.01, 0.05)  # 小目标
                h = np.random.uniform(0.01, 0.05)
            else:
                w = np.random.uniform(0.05, 0.3)   # 正常目标
                h = np.random.uniform(0.05, 0.3)
            
            x = np.random.uniform(w/2, 1-w/2)
            y = np.random.uniform(h/2, 1-h/2)
            
            gt_labels.append(f'{class_id} {x:.6f} {y:.6f} {w:.6f} {h:.6f}')
        
        # 保存GT标签
        with open(os.path.join(gt_dir, f'{img_name}.txt'), 'w') as f:
            f.write('\n'.join(gt_labels))
        
        # 创建预测标签（添加一些噪声和漏检）
        pred_labels = []
        
        for label in gt_labels:
            parts = label.split()
            class_id = int(parts[0])
            x, y, w, h = map(float, parts[1:])
            
            # 80%概率检测到目标
            if np.random.random() < 0.8:
                # 添加位置噪声
                x += np.random.normal(0, 0.02)
                y += np.random.normal(0, 0.02)
                w += np.random.normal(0, 0.01)
                h += np.random.normal(0, 0.01)
                
                # 限制在有效范围内
                x = np.clip(x, 0, 1)
                y = np.clip(y, 0, 1)
                w = np.clip(w, 0.01, 1)
                h = np.clip(h, 0.01, 1)
                
                # 随机置信度
                conf = np.random.uniform(0.5, 0.99)
                
                pred_labels.append(f'{class_id} {conf:.6f} {x:.6f} {y:.6f} {w:.6f} {h:.6f}')
        
        # 添加一些假阳性
        num_fp = np.random.randint(0, 3)
        for _ in range(num_fp):
            class_id = np.random.randint(0, 3)
            x = np.random.uniform(0, 1)
            y = np.random.uniform(0, 1)
            w = np.random.uniform(0.02, 0.1)
            h = np.random.uniform(0.02, 0.1)
            conf = np.random.uniform(0.3, 0.7)
            
            pred_labels.append(f'{class_id} {conf:.6f} {x:.6f} {y:.6f} {w:.6f} {h:.6f}')
        
        # 保存预测标签
        with open(os.path.join(pred_dir, f'{img_name}.txt'), 'w') as f:
            f.write('\n'.join(pred_labels))
    
    print(f'测试数据已创建在: {base_dir}')
    return img_dir, gt_dir, pred_dir


def test_yoloeval():
    """
    测试YOLOEval功能
    """
    print('创建测试数据...')
    img_dir, gt_dir, pred_dir = create_test_data()
    
    print('初始化评估器...')
    evaluator = YOLOEval(
        gt_dir=gt_dir,
        pred_dir=pred_dir,
        img_dir=img_dir,
        class_names=['person', 'car', 'bicycle'],
        conf_threshold=0.001
    )
    
    print('\n=== 测试IoU评估 ===')
    try:
        evaluator.evaluate(metric='iou')
        evaluator.accumulate(metric='iou')
        iou_stats = evaluator.summarize(metric='iou')
        evaluator.save_results('./test_results_iou.json', metric='iou')
        print('✅ IoU评估测试通过')
    except Exception as e:
        print(f'❌ IoU评估测试失败: {e}')
    
    print('\n=== 测试SAFit评估 ===')
    try:
        evaluator.evaluate(metric='safit')
        evaluator.accumulate(metric='safit')
        safit_stats = evaluator.summarize(metric='safit')
        evaluator.save_results('./test_results_safit.json', metric='safit')
        print('✅ SAFit评估测试通过')
    except Exception as e:
        print(f'❌ SAFit评估测试失败: {e}')
    
    print('\n=== 测试完成 ===')
    if 'iou_stats' in locals() and 'safit_stats' in locals():
        print(f'IoU AP@[0.5:0.95]: {iou_stats[0]:.3f}')
        print(f'SAFit AP@[0.5:0.95]: {safit_stats[0]:.3f}')
        print('通常SAFit指标会比IoU指标稍高，特别是对小目标')


def test_individual_functions():
    """
    测试单个函数
    """
    print('\n=== 测试单个函数 ===')
    
    evaluator = YOLOEval('.', '.', '.', conf_threshold=0.5)
    
    # 测试IoU计算
    box1 = [10, 10, 50, 50]  # x1, y1, x2, y2
    box2 = [20, 20, 60, 60]
    iou = evaluator.compute_iou(box1, box2)
    print(f'IoU测试: {iou:.3f}')
    
    # 测试NWD计算
    nwd = evaluator.compute_nwd(box1, box2)
    print(f'NWD测试: {nwd:.3f}')
    
    # 测试SAFit计算
    safit = evaluator.compute_safit(box1, box2)
    print(f'SAFit测试: {safit:.3f}')
    
    print('✅ 单个函数测试通过')


def evaluate_rgbt_tiny_model(model_path=None, data_config_path=None, generate_predictions=True):
    """
    使用训练好的best.pt模型评估RGBT-Tiny数据集
    """
    # 根据数据配置确定模态类型
    if data_config_path and 'ir' in data_config_path:
        modality = 'IR'
        default_gt_dir = '/media/lenovo/KIOXIA/ZPD/Dataset-small/RGBT-Tiny-yolo_partial/labels/val/thermal'
        default_img_dir = '/media/lenovo/KIOXIA/ZPD/Dataset-small/RGBT-Tiny-yolo_partial/images/val/thermal'
        pred_dir = './eval_predictions_ir'
    else:
        modality = 'RGB'
        default_gt_dir = '/media/lenovo/KIOXIA/ZPD/Dataset-small/RGBT-Tiny-yolo_partial/labels/val/rgb'
        default_img_dir = '/media/lenovo/KIOXIA/ZPD/Dataset-small/RGBT-Tiny-yolo_partial/images/val/rgb'
        pred_dir = './eval_predictions_rgb'

    print(f'🚀 YOLOv8 RGBT-Tiny {modality}模态评估')
    print('=' * 60)

    # 配置路径 - 根据您的实际数据结构
    gt_dir = default_gt_dir
    img_dir = default_img_dir

    # 如果提供了数据配置文件，尝试从中读取路径
    if data_config_path:
        try:
            import yaml
            with open(data_config_path, 'r') as f:
                data_config = yaml.safe_load(f)

            dataset_path = data_config['path']
            val_path = data_config['val']

            # 构建实际路径
            if os.path.isabs(val_path):
                img_dir = val_path
            else:
                img_dir = os.path.join(dataset_path, val_path)

            # GT标签路径
            gt_dir = img_dir.replace('images', 'labels')

            print(f'📁 从配置文件读取路径: {data_config_path}')
        except Exception as e:
            print(f'⚠️ 读取配置文件失败，使用默认路径: {e}')

    # 模型路径
    if model_path is None:
        model_path = 'runs/detect/RGBT_Tiny_RGB_yolov8n/weights/best.pt'

    # RGBT-Tiny数据集类别（按照您的数据配置）
    class_names = ['bus', 'car', 'cyclist', 'drone', 'pedestrian', 'plane', 'ship']

    print(f'📁 GT标签目录: {gt_dir}')
    print(f'📁 预测结果目录: {pred_dir}')
    print(f'📁 图像目录: {img_dir}')
    print(f'📁 模型路径: {model_path}')
    print(f'📊 数据集类别: {class_names}')

    # 检查路径是否存在
    if not os.path.exists(gt_dir):
        print(f'❌ GT标签目录不存在: {gt_dir}')
        return
    if not os.path.exists(img_dir):
        print(f'❌ 图像目录不存在: {img_dir}')
        return
    if not os.path.exists(model_path):
        print(f'❌ 模型文件不存在: {model_path}')
        return

    print('✅ 基础路径验证通过')

    # 生成预测结果
    if generate_predictions or not os.path.exists(pred_dir):
        print('\n🤖 使用YOLOv8模型生成预测结果...')
        success = generate_yolov8_predictions(model_path, img_dir, pred_dir)
        if not success:
            print('❌ 预测生成失败')
            return
        print('✅ 预测结果生成完成')
    else:
        print('✅ 使用现有预测结果')

    # 创建评估器
    print('\n🔧 创建评估器...')
    evaluator = YOLOEval(
        gt_dir=gt_dir,
        pred_dir=pred_dir,
        img_dir=img_dir,
        class_names=class_names,
        conf_threshold=0.001  # 小目标检测使用极低阈值
    )

    print('✅ 评估器创建成功')

    # IoU评估
    print('\n=== 🎯 IoU指标评估 ===')
    try:
        evaluator.evaluate(metric='iou')
        evaluator.accumulate(metric='iou')
        iou_stats = evaluator.summarize(metric='iou')
        evaluator.save_results('./rgbt_tiny_iou_results.json', metric='iou')
        print('✅ IoU评估完成')
    except Exception as e:
        print(f'❌ IoU评估失败: {e}')
        return

    # SAFit评估
    print('\n=== 🎯 SAFit指标评估 ===')
    try:
        evaluator.evaluate(metric='safit')
        evaluator.accumulate(metric='safit')
        safit_stats = evaluator.summarize(metric='safit')
        evaluator.save_results('./rgbt_tiny_safit_results.json', metric='safit')
        print('✅ SAFit评估完成')
    except Exception as e:
        print(f'❌ SAFit评估失败: {e}')
        return

    # 结果对比
    print('\n=== 📊 评估结果对比 ===')
    print(f'IoU mAP@[0.5:0.95]:   {iou_stats[0]:.3f}')
    print(f'SAFit mAP@[0.5:0.95]: {safit_stats[0]:.3f}')
    print(f'SAFit提升:            {safit_stats[0] - iou_stats[0]:.3f}')

    # 小目标性能分析
    print('\n=== 🔍 小目标性能分析 ===')
    print('IoU指标:')
    print(f'  • Extreme Tiny AP: {iou_stats[3]:.3f}')
    print(f'  • Tiny AP:         {iou_stats[4]:.3f}')
    print(f'  • Small AP:        {iou_stats[5]:.3f}')
    print('SAFit指标:')
    print(f'  • Extreme Tiny AP: {safit_stats[3]:.3f}')
    print(f'  • Tiny AP:         {safit_stats[4]:.3f}')
    print(f'  • Small AP:        {safit_stats[5]:.3f}')

    print('\n✅ 评估完成！')
    print('📄 结果文件:')
    print('  • rgbt_tiny_iou_results.json')
    print('  • rgbt_tiny_safit_results.json')


if __name__ == '__main__':
    import argparse

    parser = argparse.ArgumentParser(description='YOLOEval测试和评估脚本')
    parser.add_argument('--mode', type=str, default='test', choices=['test', 'eval'],
                        help='运行模式: test=测试功能, eval=评估RGBT-Tiny模型')
    parser.add_argument('--model', type=str,
                        default='runs/detect/RGBT_Tiny_RGB_yolov8n/weights/best.pt',
                        help='模型文件路径')
    parser.add_argument('--data', type=str,
                        default='./data/rgbt_tiny_rgb.yaml',
                        help='数据集配置文件路径')
    parser.add_argument('--no-generate', action='store_true',
                        help='不重新生成预测结果，使用现有结果')

    args = parser.parse_args()

    if args.mode == 'test':
        print('YOLOEval 测试脚本')
        print('=' * 50)

        # 测试单个函数
        test_individual_functions()

        # 测试完整流程
        test_yoloeval()

        print('\n所有测试完成！')
        print('如果看到错误，请检查依赖包是否安装完整：')
        print('pip install numpy opencv-python tqdm')

    elif args.mode == 'eval':
        if not ULTRALYTICS_AVAILABLE:
            print('❌ 错误: ultralytics未安装，无法进行模型评估')
            print('请安装: pip install ultralytics')
            exit(1)

        # 评估RGBT-Tiny模型
        evaluate_rgbt_tiny_model(
            model_path=args.model,
            data_config_path=args.data,
            generate_predictions=not args.no_generate
        )
