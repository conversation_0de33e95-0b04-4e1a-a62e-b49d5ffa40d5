#!/usr/bin/env python3
"""
快速评估脚本 - 一键从best.pt到评估结果
"""

import os
import argparse
from inference_and_eval import main_pipeline


def quick_evaluate():
    """
    快速评估函数
    """
    parser = argparse.ArgumentParser(description='快速评估YOLO模型')
    parser.add_argument('--model', type=str, required=True, help='模型文件路径 (best.pt)')
    parser.add_argument('--images', type=str, required=True, help='测试图像目录')
    parser.add_argument('--labels', type=str, required=True, help='GT标签目录')
    parser.add_argument('--output', type=str, default='./eval_output', help='输出目录')
    parser.add_argument('--conf', type=float, default=0.001, help='置信度阈值')
    
    args = parser.parse_args()
    
    print("🎯 快速评估YOLO模型")
    print("=" * 50)
    print(f"模型文件: {args.model}")
    print(f"图像目录: {args.images}")
    print(f"标签目录: {args.labels}")
    print(f"输出目录: {args.output}")
    print(f"置信度阈值: {args.conf}")
    print("=" * 50)
    
    # 运行评估
    results = main_pipeline(
        model_path=args.model,
        img_dir=args.images,
        gt_dir=args.labels,
        output_dir=args.output,
        conf_threshold=args.conf
    )
    
    if results:
        print("\n✅ 评估成功完成！")
    else:
        print("\n❌ 评估失败，请检查输入参数")


if __name__ == '__main__':
    # 如果没有命令行参数，使用默认配置
    import sys
    if len(sys.argv) == 1:
        print("🔧 使用默认配置运行...")
        print("如需自定义参数，请使用:")
        print("python quick_eval.py --model best.pt --images ./images --labels ./labels")
        print()
        
        # 默认配置
        model_path = './best.pt'
        img_dir = './data/images'
        gt_dir = './data/labels/gt'
        output_dir = './eval_output'
        
        # 检查文件是否存在
        if not os.path.exists(model_path):
            print(f"❌ 模型文件不存在: {model_path}")
            print("请将您的best.pt文件放在当前目录，或使用 --model 参数指定路径")
            sys.exit(1)
        
        if not os.path.exists(img_dir):
            print(f"❌ 图像目录不存在: {img_dir}")
            print("请创建图像目录并放入测试图像，或使用 --images 参数指定路径")
            sys.exit(1)
        
        if not os.path.exists(gt_dir):
            print(f"❌ GT标签目录不存在: {gt_dir}")
            print("请创建GT标签目录并放入YOLO格式标签，或使用 --labels 参数指定路径")
            sys.exit(1)
        
        # 运行评估
        main_pipeline(model_path, img_dir, gt_dir, output_dir)
    else:
        quick_evaluate()
