# 通用SAFit评估器配置文件模板
# 复制此文件并根据您的项目进行修改

# ==================== 基础配置 ====================
device: 'cuda'  # 'cuda' 或 'cpu'
output_dir: './evaluation_output'  # 评估结果输出目录

# ==================== 模型配置 ====================
model_path: './runs/train/exp/weights/best.pt'  # 训练好的模型权重路径
model_type: 'auto'  # 模型类型: 'auto', 'yolo', 'mmdet', 'torch'

# MMDetection专用配置（如果使用MMDetection框架）
mmdet_config: null  # MMDetection配置文件路径，例如: './configs/faster_rcnn_r50_fpn_1x_coco.py'

# ==================== 数据配置 ====================
# 测试数据路径
test_images_dir: './data/test/images'     # 测试图像目录
gt_labels_dir: './data/test/labels'       # GT标签目录（YOLO格式）

# 双模态专用配置
thermal_images_dir: './data/test/thermal' # 热红外图像目录（双模态时使用）

# ==================== 模态配置 ====================
dual_modal: false  # 是否为双模态(RGB-T)评估
# true: 双模态评估，需要RGB和热红外图像
# false: 单模态评估，只使用RGB图像

# ==================== 类别配置 ====================
# 根据您的数据集修改类别名称
class_names:
  - 'person'
  - 'car'
  - 'bicycle'
  - 'motorcycle'
  - 'bus'
  - 'truck'

# ==================== 评估配置 ====================
conf_threshold: 0.001  # 置信度阈值
img_size: 640         # 输入图像尺寸

# ==================== 高级配置 ====================
# 自定义预处理参数（可选）
preprocessing:
  normalize: true
  mean: [0.485, 0.456, 0.406]  # ImageNet标准化均值
  std: [0.229, 0.224, 0.225]   # ImageNet标准化标准差

# 批处理配置
batch_size: 1  # 推理批大小（通常设为1以避免内存问题）

# ==================== 示例配置 ====================
# 以下是一些常见算法的配置示例，取消注释并修改相应部分

# YOLOv5示例:
# model_path: './yolov5/runs/train/exp/weights/best.pt'
# model_type: 'yolo'
# img_size: 640

# YOLOv8示例:
# model_path: './yolov8_best.pt'
# model_type: 'yolo'
# img_size: 640

# MMDetection示例:
# model_path: './work_dirs/faster_rcnn/latest.pth'
# model_type: 'mmdet'
# mmdet_config: './configs/faster_rcnn_r50_fpn_1x_coco.py'

# 自定义PyTorch模型示例:
# model_path: './checkpoints/model_best.pth'
# model_type: 'torch'

# 双模态RGBT示例:
# dual_modal: true
# test_images_dir: './data/test/rgb'
# thermal_images_dir: './data/test/thermal'
