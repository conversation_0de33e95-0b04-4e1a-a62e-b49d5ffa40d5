#!/usr/bin/env python3
"""
通用SAFit评估器 - 支持任何算法的best.pt权重文件
支持双模态(RGB-T)和单模态评估
只需修改配置即可使用
"""

import os
import sys
import torch
import cv2
import numpy as np
from pathlib import Path
import argparse
import yaml
from tqdm import tqdm
import json
import datetime

# 添加RGBT-Tiny评估代码路径
sys.path.append('codes')
from metric.yoloeval import YOLOEval

class UniversalSAFitEvaluator:
    """
    通用SAFit评估器
    支持加载任何算法的best.pt权重文件进行评估
    """
    
    def __init__(self, config):
        """
        初始化评估器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.device = torch.device(config['device'])
        self.model = None
        self.class_names = config['class_names']
        self.is_dual_modal = config.get('dual_modal', False)
        
        # 创建输出目录
        self.output_dir = Path(config['output_dir'])
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.pred_dir = self.output_dir / "predictions"
        self.pred_dir.mkdir(exist_ok=True)
        
        print(f"🚀 初始化通用SAFit评估器")
        print(f"📁 输出目录: {self.output_dir}")
        print(f"🔧 设备: {self.device}")
        print(f"🎯 模态: {'双模态(RGB-T)' if self.is_dual_modal else '单模态'}")
        
    def load_model(self, model_path, model_type='auto'):
        """
        加载训练好的模型
        
        Args:
            model_path: 模型权重路径 (best.pt)
            model_type: 模型类型 ('yolo', 'mmdet', 'custom', 'auto')
        """
        print(f"📦 加载模型: {model_path}")
        
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"模型文件不存在: {model_path}")
        
        # 自动检测模型类型
        if model_type == 'auto':
            model_type = self._detect_model_type(model_path)
            print(f"🔍 自动检测模型类型: {model_type}")
        
        # 根据模型类型加载
        if model_type == 'yolo':
            self.model = self._load_yolo_model(model_path)
        elif model_type == 'mmdet':
            self.model = self._load_mmdet_model(model_path)
        elif model_type == 'torch':
            self.model = self._load_torch_model(model_path)
        else:
            # 通用PyTorch模型加载
            self.model = self._load_generic_model(model_path)
        
        self.model.to(self.device)
        self.model.eval()
        print("✅ 模型加载完成")
        
    def _detect_model_type(self, model_path):
        """自动检测模型类型"""
        try:
            checkpoint = torch.load(model_path, map_location='cpu')
            
            # 检查是否是YOLOv5/YOLOv8格式
            if 'model' in checkpoint and hasattr(checkpoint.get('model'), 'yaml'):
                return 'yolo'
            
            # 检查是否是MMDetection格式
            if 'meta' in checkpoint and 'config' in checkpoint.get('meta', {}):
                return 'mmdet'
            
            # 默认为通用PyTorch模型
            return 'torch'
            
        except Exception as e:
            print(f"⚠️ 模型类型检测失败: {e}")
            return 'torch'
    
    def _load_yolo_model(self, model_path):
        """加载YOLO模型"""
        try:
            # 尝试使用ultralytics
            from ultralytics import YOLO
            model = YOLO(model_path)
            return model
        except ImportError:
            try:
                # 尝试使用YOLOv5
                import torch
                model = torch.hub.load('ultralytics/yolov5', 'custom', model_path)
                return model
            except Exception as e:
                print(f"⚠️ YOLO模型加载失败: {e}")
                return self._load_torch_model(model_path)
    
    def _load_mmdet_model(self, model_path):
        """加载MMDetection模型"""
        try:
            from mmdet.apis import init_detector
            config_file = self.config.get('mmdet_config', None)
            if config_file and os.path.exists(config_file):
                model = init_detector(config_file, model_path, device=self.device)
                return model
            else:
                print("⚠️ MMDetection配置文件未找到，使用通用加载方式")
                return self._load_torch_model(model_path)
        except ImportError:
            print("⚠️ MMDetection未安装，使用通用加载方式")
            return self._load_torch_model(model_path)
    
    def _load_torch_model(self, model_path):
        """加载通用PyTorch模型"""
        checkpoint = torch.load(model_path, map_location=self.device)
        
        # 尝试不同的键名
        if 'model' in checkpoint:
            model = checkpoint['model']
        elif 'state_dict' in checkpoint:
            # 需要模型架构，这里需要用户提供
            model = self._create_model_architecture()
            model.load_state_dict(checkpoint['state_dict'])
        else:
            # 直接是模型
            model = checkpoint
        
        return model
    
    def _load_generic_model(self, model_path):
        """通用模型加载方法"""
        # 这里需要用户根据自己的模型架构进行定制
        # 示例代码，需要根据具体模型调整
        print("⚠️ 使用通用模型加载，可能需要手动调整")
        return torch.load(model_path, map_location=self.device)
    
    def _create_model_architecture(self):
        """
        创建模型架构
        用户需要根据自己的模型进行定制
        """
        # 这里需要用户提供模型架构代码
        # 示例：
        # from your_model import YourModel
        # return YourModel(num_classes=len(self.class_names))
        
        raise NotImplementedError("请在此处添加您的模型架构代码")
    
    def run_evaluation(self):
        """
        执行完整的评估流程
        """
        print("🔄 开始评估流程...")
        
        # 1. 生成预测结果
        self._generate_predictions()
        
        # 2. 执行SAFit评估
        results = self._run_safit_evaluation()
        
        # 3. 保存结果
        self._save_results(results)
        
        return results
    
    def _generate_predictions(self):
        """生成预测结果"""
        print("📊 生成预测结果...")
        
        # 获取测试图像
        test_images = self._get_test_images()
        
        for img_info in tqdm(test_images, desc="处理图像"):
            img_name = img_info['name']
            
            # 加载图像
            if self.is_dual_modal:
                rgb_img, thermal_img = self._load_dual_modal_image(img_info)
                predictions = self._predict_dual_modal(rgb_img, thermal_img)
            else:
                img = self._load_single_modal_image(img_info)
                predictions = self._predict_single_modal(img)
            
            # 转换为YOLO格式并保存
            yolo_preds = self._convert_to_yolo_format(
                predictions, img_info['width'], img_info['height']
            )
            self._save_predictions(img_name, yolo_preds)
    
    def _get_test_images(self):
        """获取测试图像列表"""
        test_images = []
        img_dir = self.config['test_images_dir']
        
        # 支持的图像格式
        img_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
        
        for ext in img_extensions:
            for img_path in Path(img_dir).glob(f"*{ext}"):
                # 获取图像尺寸
                img = cv2.imread(str(img_path))
                if img is not None:
                    h, w = img.shape[:2]
                    test_images.append({
                        'name': img_path.stem,
                        'path': str(img_path),
                        'width': w,
                        'height': h
                    })
        
        print(f"📸 找到 {len(test_images)} 张测试图像")
        return test_images
    
    def _load_single_modal_image(self, img_info):
        """加载单模态图像"""
        img = cv2.imread(img_info['path'])
        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        return img
    
    def _load_dual_modal_image(self, img_info):
        """加载双模态图像"""
        # RGB图像
        rgb_path = img_info['path']
        rgb_img = cv2.imread(rgb_path)
        rgb_img = cv2.cvtColor(rgb_img, cv2.COLOR_BGR2RGB)
        
        # 热红外图像 (假设在thermal子目录中)
        thermal_dir = self.config.get('thermal_images_dir', 
                                    str(Path(self.config['test_images_dir']) / 'thermal'))
        thermal_path = os.path.join(thermal_dir, f"{img_info['name']}.jpg")
        
        if os.path.exists(thermal_path):
            thermal_img = cv2.imread(thermal_path)
            thermal_img = cv2.cvtColor(thermal_img, cv2.COLOR_BGR2RGB)
        else:
            print(f"⚠️ 热红外图像未找到: {thermal_path}")
            thermal_img = rgb_img.copy()  # 使用RGB图像作为备选
        
        return rgb_img, thermal_img
    
    def _predict_single_modal(self, img):
        """单模态预测"""
        # 预处理
        input_tensor = self._preprocess_image(img)
        
        # 推理
        with torch.no_grad():
            predictions = self.model(input_tensor)
        
        return predictions
    
    def _predict_dual_modal(self, rgb_img, thermal_img):
        """双模态预测"""
        # 预处理
        rgb_tensor = self._preprocess_image(rgb_img)
        thermal_tensor = self._preprocess_image(thermal_img)
        
        # 推理 (需要根据具体模型调整)
        with torch.no_grad():
            if hasattr(self.model, 'forward_dual'):
                predictions = self.model.forward_dual(rgb_tensor, thermal_tensor)
            else:
                # 如果模型不支持双模态，可以尝试拼接
                dual_input = torch.cat([rgb_tensor, thermal_tensor], dim=1)
                predictions = self.model(dual_input)
        
        return predictions
    
    def _preprocess_image(self, img):
        """图像预处理"""
        # 基础预处理，根据模型需求调整
        img_size = self.config.get('img_size', 640)
        
        # 调整尺寸
        img_resized = cv2.resize(img, (img_size, img_size))
        
        # 归一化
        img_normalized = img_resized.astype(np.float32) / 255.0
        
        # 转换为tensor
        img_tensor = torch.from_numpy(img_normalized).permute(2, 0, 1).unsqueeze(0)
        img_tensor = img_tensor.to(self.device)
        
        return img_tensor
    
    def _convert_to_yolo_format(self, predictions, img_width, img_height):
        """
        将预测结果转换为YOLO格式
        需要根据具体模型输出格式调整
        """
        yolo_preds = []
        
        # 这里需要根据您的模型输出格式进行调整
        # 以下是通用示例，请根据实际情况修改
        
        try:
            # 尝试处理不同格式的输出
            if hasattr(predictions, 'pred'):  # YOLOv5格式
                pred = predictions.pred[0].cpu().numpy()
                for detection in pred:
                    if len(detection) >= 6:
                        x1, y1, x2, y2, conf, cls = detection[:6]
                        yolo_preds.append(self._bbox_to_yolo(
                            x1, y1, x2, y2, conf, int(cls), img_width, img_height
                        ))
            
            elif isinstance(predictions, torch.Tensor):  # 直接tensor输出
                pred = predictions.cpu().numpy()
                # 需要根据具体格式解析
                pass
            
            elif isinstance(predictions, dict):  # 字典格式输出
                if 'boxes' in predictions:  # Faster R-CNN等格式
                    boxes = predictions['boxes'].cpu().numpy()
                    scores = predictions['scores'].cpu().numpy()
                    labels = predictions['labels'].cpu().numpy()
                    
                    for box, score, label in zip(boxes, scores, labels):
                        x1, y1, x2, y2 = box
                        yolo_preds.append(self._bbox_to_yolo(
                            x1, y1, x2, y2, score, int(label), img_width, img_height
                        ))
            
        except Exception as e:
            print(f"⚠️ 预测结果转换失败: {e}")
            print(f"预测结果类型: {type(predictions)}")
            # 返回空预测
            pass
        
        return yolo_preds
    
    def _bbox_to_yolo(self, x1, y1, x2, y2, conf, cls, img_width, img_height):
        """将边界框转换为YOLO格式"""
        # 转换为归一化的中心点坐标
        x_center = (x1 + x2) / 2 / img_width
        y_center = (y1 + y2) / 2 / img_height
        width = (x2 - x1) / img_width
        height = (y2 - y1) / img_height
        
        return {
            'class_id': cls,
            'confidence': float(conf),
            'x_center': float(x_center),
            'y_center': float(y_center),
            'width': float(width),
            'height': float(height)
        }
    
    def _save_predictions(self, img_name, predictions):
        """保存预测结果"""
        pred_file = self.pred_dir / f"{img_name}.txt"
        
        with open(pred_file, 'w') as f:
            for pred in predictions:
                f.write(f"{pred['class_id']} {pred['confidence']:.6f} "
                       f"{pred['x_center']:.6f} {pred['y_center']:.6f} "
                       f"{pred['width']:.6f} {pred['height']:.6f}\n")
    
    def _run_safit_evaluation(self):
        """执行SAFit评估"""
        print("📊 执行SAFit评估...")
        
        evaluator = YOLOEval(
            gt_dir=self.config['gt_labels_dir'],
            pred_dir=str(self.pred_dir),
            img_dir=self.config['test_images_dir'],
            class_names=self.class_names,
            conf_threshold=self.config.get('conf_threshold', 0.001)
        )
        
        results = {}
        
        # SAFit评估
        evaluator.evaluate(metric='safit')
        evaluator.accumulate(metric='safit')
        safit_stats = evaluator.summarize(metric='safit')
        results['safit'] = safit_stats
        
        # IoU评估（对比用）
        evaluator.evaluate(metric='iou')
        evaluator.accumulate(metric='iou')
        iou_stats = evaluator.summarize(metric='iou')
        results['iou'] = iou_stats
        
        return results
    
    def _save_results(self, results):
        """保存评估结果"""
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存详细结果
        results_file = self.output_dir / f"evaluation_results_{timestamp}.json"
        with open(results_file, 'w') as f:
            json_results = {
                'safit': results['safit'].tolist() if isinstance(results['safit'], np.ndarray) else results['safit'],
                'iou': results['iou'].tolist() if isinstance(results['iou'], np.ndarray) else results['iou'],
                'config': self.config
            }
            json.dump(json_results, f, indent=2)
        
        # 打印结果摘要
        self._print_results(results)
        
        print(f"💾 结果已保存到: {results_file}")
    
    def _print_results(self, results):
        """打印评估结果"""
        print("\n" + "="*80)
        print("📈 SAFit评估结果")
        print("="*80)
        
        safit_stats = results['safit']
        iou_stats = results['iou']
        
        print(f"SAFit AP@[0.5:0.95]:  {safit_stats[0]:.3f}")
        print(f"SAFit AP@0.5:        {safit_stats[1]:.3f}")
        print(f"SAFit AP@0.75:       {safit_stats[2]:.3f}")
        print(f"SAFit AP_tiny:       {safit_stats[4]:.3f}")
        print(f"SAFit AP_small:      {safit_stats[5]:.3f}")
        
        print(f"\nIoU AP@[0.5:0.95]:    {iou_stats[0]:.3f}")
        print(f"IoU AP@0.5:          {iou_stats[1]:.3f}")
        print(f"IoU AP@0.75:         {iou_stats[2]:.3f}")
        print(f"IoU AP_tiny:         {iou_stats[4]:.3f}")
        print(f"IoU AP_small:        {iou_stats[5]:.3f}")
        
        improvement = (safit_stats[0] / iou_stats[0] - 1) * 100 if iou_stats[0] > 0 else 0
        print(f"\n🎯 SAFit相比IoU提升: {improvement:+.1f}%")


def create_config_template():
    """创建配置文件模板"""
    config = {
        # 基础配置
        'device': 'cuda' if torch.cuda.is_available() else 'cpu',
        'output_dir': './evaluation_output',
        
        # 模型配置
        'model_path': './best.pt',  # 训练好的模型权重路径
        'model_type': 'auto',  # 'auto', 'yolo', 'mmdet', 'torch'
        'mmdet_config': None,  # MMDetection配置文件路径（如果使用MMDet）
        
        # 数据配置
        'test_images_dir': './data/test/images',  # 测试图像目录
        'gt_labels_dir': './data/test/labels',   # GT标签目录
        'thermal_images_dir': './data/test/thermal',  # 热红外图像目录（双模态时使用）
        
        # 模态配置
        'dual_modal': False,  # 是否为双模态(RGB-T)
        
        # 类别配置
        'class_names': ['person', 'car', 'bicycle'],  # 根据您的数据集修改
        
        # 评估配置
        'conf_threshold': 0.001,
        'img_size': 640,
    }
    
    return config


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='通用SAFit评估器')
    parser.add_argument('--config', type=str, help='配置文件路径')
    parser.add_argument('--model', type=str, help='模型权重路径')
    parser.add_argument('--data', type=str, help='测试数据目录')
    parser.add_argument('--dual-modal', action='store_true', help='启用双模态评估')
    
    args = parser.parse_args()
    
    # 创建配置
    if args.config and os.path.exists(args.config):
        with open(args.config, 'r') as f:
            config = yaml.safe_load(f)
    else:
        config = create_config_template()
        
        # 命令行参数覆盖
        if args.model:
            config['model_path'] = args.model
        if args.data:
            config['test_images_dir'] = args.data
        if args.dual_modal:
            config['dual_modal'] = True
    
    # 创建评估器并运行
    evaluator = UniversalSAFitEvaluator(config)
    evaluator.load_model(config['model_path'], config['model_type'])
    results = evaluator.run_evaluation()
