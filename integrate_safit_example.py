#!/usr/bin/env python3
"""
将SAFit评估集成到现有算法的示例
适用于YOLOv5, YOLOv8, DETR, Faster R-CNN等任何目标检测算法
"""

import os
import torch
import numpy as np
from pathlib import Path
import cv2

# 假设这是您现有的算法模块
# from your_algorithm import YourDetectionModel

class SAFitIntegration:
    """
    SAFit评估集成类
    可以轻松集成到任何现有的目标检测项目中
    """
    
    def __init__(self, model, device='cuda'):
        """
        初始化
        
        Args:
            model: 您的检测模型
            device: 计算设备
        """
        self.model = model
        self.device = device
        self.model.eval()
        
    def generate_predictions_and_evaluate(self, 
                                        test_images_dir,
                                        gt_labels_dir, 
                                        class_names,
                                        conf_threshold=0.001,
                                        img_size=640):
        """
        生成预测结果并进行SAFit评估
        
        Args:
            test_images_dir: 测试图像目录
            gt_labels_dir: GT标签目录
            class_names: 类别名称列表
            conf_threshold: 置信度阈值
            img_size: 输入图像尺寸
        """
        
        # 1. 生成预测结果
        print("🔄 正在生成预测结果...")
        pred_dir = "temp_predictions"
        os.makedirs(pred_dir, exist_ok=True)
        
        self._generate_yolo_predictions(test_images_dir, pred_dir, conf_threshold, img_size)
        
        # 2. 执行SAFit评估
        print("📊 执行SAFit评估...")
        from safit_evaluation_only import SAFitEvaluator
        
        evaluator = SAFitEvaluator()
        results = evaluator.evaluate_predictions(
            gt_dir=gt_labels_dir,
            pred_dir=pred_dir,
            img_dir=test_images_dir,
            class_names=class_names,
            conf_threshold=conf_threshold
        )
        
        return results
    
    def _generate_yolo_predictions(self, images_dir, output_dir, conf_threshold, img_size):
        """
        生成YOLO格式的预测结果
        """
        image_files = []
        for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
            image_files.extend([f for f in os.listdir(images_dir) if f.lower().endswith(ext)])
        
        for img_file in image_files:
            img_path = os.path.join(images_dir, img_file)
            img_name = os.path.splitext(img_file)[0]
            
            # 读取和预处理图像
            img = cv2.imread(img_path)
            if img is None:
                continue
                
            original_h, original_w = img.shape[:2]
            
            # 这里需要根据您的模型调整预处理方式
            processed_img = self._preprocess_image(img, img_size)
            
            # 模型推理
            with torch.no_grad():
                predictions = self._run_inference(processed_img)
            
            # 后处理并转换为YOLO格式
            yolo_predictions = self._postprocess_to_yolo(
                predictions, original_w, original_h, conf_threshold
            )
            
            # 保存预测结果
            output_file = os.path.join(output_dir, f"{img_name}.txt")
            self._save_yolo_predictions(yolo_predictions, output_file)
    
    def _preprocess_image(self, img, img_size):
        """
        图像预处理 - 根据您的模型调整
        """
        # 示例预处理（需要根据您的模型调整）
        img_resized = cv2.resize(img, (img_size, img_size))
        img_rgb = cv2.cvtColor(img_resized, cv2.COLOR_BGR2RGB)
        img_tensor = torch.from_numpy(img_rgb).permute(2, 0, 1).float() / 255.0
        img_tensor = img_tensor.unsqueeze(0).to(self.device)
        return img_tensor
    
    def _run_inference(self, img_tensor):
        """
        模型推理 - 根据您的模型调整
        """
        # 这里需要根据您的具体模型调整
        # 示例：
        # return self.model(img_tensor)
        
        # 占位符 - 请替换为您的模型推理代码
        # 返回格式应该是您模型的原始输出
        pass
    
    def _postprocess_to_yolo(self, predictions, original_w, original_h, conf_threshold):
        """
        后处理并转换为YOLO格式
        
        Args:
            predictions: 模型原始输出
            original_w, original_h: 原始图像尺寸
            conf_threshold: 置信度阈值
            
        Returns:
            list: YOLO格式的预测结果
        """
        yolo_predictions = []
        
        # 这里需要根据您的模型输出格式调整
        # 以下是一个通用的示例，假设predictions包含边界框和置信度
        
        # 示例：假设predictions是一个包含检测结果的列表
        # 每个检测结果包含: [x1, y1, x2, y2, confidence, class_id]
        
        # for detection in predictions:
        #     x1, y1, x2, y2, confidence, class_id = detection
        #     
        #     if confidence < conf_threshold:
        #         continue
        #     
        #     # 转换为归一化的中心点坐标格式
        #     x_center = (x1 + x2) / 2 / original_w
        #     y_center = (y1 + y2) / 2 / original_h
        #     width = (x2 - x1) / original_w
        #     height = (y2 - y1) / original_h
        #     
        #     yolo_predictions.append({
        #         'class_id': int(class_id),
        #         'confidence': float(confidence),
        #         'x_center': float(x_center),
        #         'y_center': float(y_center),
        #         'width': float(width),
        #         'height': float(height)
        #     })
        
        return yolo_predictions
    
    def _save_yolo_predictions(self, predictions, output_file):
        """
        保存YOLO格式的预测结果
        """
        with open(output_file, 'w') as f:
            for pred in predictions:
                f.write(f"{pred['class_id']} {pred['confidence']:.6f} "
                       f"{pred['x_center']:.6f} {pred['y_center']:.6f} "
                       f"{pred['width']:.6f} {pred['height']:.6f}\n")

# 具体算法集成示例

class YOLOv5SAFitIntegration(SAFitIntegration):
    """YOLOv5 + SAFit评估集成示例"""
    
    def _run_inference(self, img_tensor):
        """YOLOv5推理"""
        return self.model(img_tensor)
    
    def _postprocess_to_yolo(self, predictions, original_w, original_h, conf_threshold):
        """YOLOv5后处理"""
        yolo_predictions = []
        
        # YOLOv5输出格式处理
        pred = predictions[0]  # 假设batch_size=1
        
        for detection in pred:
            x1, y1, x2, y2, confidence, class_id = detection[:6]
            
            if confidence < conf_threshold:
                continue
            
            # 转换为归一化坐标
            x_center = (x1 + x2) / 2 / original_w
            y_center = (y1 + y2) / 2 / original_h
            width = (x2 - x1) / original_w
            height = (y2 - y1) / original_h
            
            yolo_predictions.append({
                'class_id': int(class_id),
                'confidence': float(confidence),
                'x_center': float(x_center),
                'y_center': float(y_center),
                'width': float(width),
                'height': float(height)
            })
        
        return yolo_predictions

class FasterRCNNSAFitIntegration(SAFitIntegration):
    """Faster R-CNN + SAFit评估集成示例"""
    
    def _postprocess_to_yolo(self, predictions, original_w, original_h, conf_threshold):
        """Faster R-CNN后处理"""
        yolo_predictions = []
        
        # Faster R-CNN输出格式处理
        boxes = predictions['boxes'].cpu().numpy()
        scores = predictions['scores'].cpu().numpy()
        labels = predictions['labels'].cpu().numpy()
        
        for box, score, label in zip(boxes, scores, labels):
            if score < conf_threshold:
                continue
            
            x1, y1, x2, y2 = box
            
            # 转换为归一化坐标
            x_center = (x1 + x2) / 2 / original_w
            y_center = (y1 + y2) / 2 / original_h
            width = (x2 - x1) / original_w
            height = (y2 - y1) / original_h
            
            yolo_predictions.append({
                'class_id': int(label),
                'confidence': float(score),
                'x_center': float(x_center),
                'y_center': float(y_center),
                'width': float(width),
                'height': float(height)
            })
        
        return yolo_predictions

# 使用示例
if __name__ == "__main__":
    # 示例1: YOLOv5集成
    # model = torch.hub.load('ultralytics/yolov5', 'yolov5s', pretrained=True)
    # integration = YOLOv5SAFitIntegration(model)
    
    # 示例2: 自定义模型集成
    # model = YourCustomModel()
    # integration = SAFitIntegration(model)
    
    # 执行评估
    # results = integration.generate_predictions_and_evaluate(
    #     test_images_dir="data/test/images",
    #     gt_labels_dir="data/test/labels", 
    #     class_names=['person', 'car', 'bicycle'],
    #     conf_threshold=0.001
    # )
    
    print("请根据您的具体算法调整相应的推理和后处理代码")
