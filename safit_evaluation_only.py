#!/usr/bin/env python3
"""
SAFit评估专用脚本 - 适用于任何目标检测算法
只进行评估，不涉及训练过程
"""

import os
import sys
import numpy as np
import json
from pathlib import Path

# 添加codes路径到系统路径
sys.path.append('codes')
from metric.yoloeval import YOLOEval

class SAFitEvaluator:
    """
    SAFit专用评估器
    可以集成到任何现有的目标检测项目中
    """
    
    def __init__(self, project_root=None):
        """
        初始化评估器
        
        Args:
            project_root: 项目根目录，如果为None则使用当前目录
        """
        self.project_root = Path(project_root) if project_root else Path.cwd()
        self.results_dir = self.project_root / "evaluation_results"
        self.results_dir.mkdir(exist_ok=True)
        
    def evaluate_predictions(self, 
                           gt_dir, 
                           pred_dir, 
                           img_dir, 
                           class_names=None,
                           conf_threshold=0.001,
                           save_results=True):
        """
        评估预测结果
        
        Args:
            gt_dir: GT标签目录 (YOLO格式)
            pred_dir: 预测结果目录 (YOLO格式)
            img_dir: 图像目录
            class_names: 类别名称列表
            conf_threshold: 置信度阈值
            save_results: 是否保存详细结果
            
        Returns:
            dict: 包含SAFit和IoU评估结果的字典
        """
        print("🚀 开始SAFit评估...")
        print(f"GT目录: {gt_dir}")
        print(f"预测目录: {pred_dir}")
        print(f"图像目录: {img_dir}")
        
        # 检查路径
        for path, name in [(gt_dir, 'GT标签'), (pred_dir, '预测结果'), (img_dir, '图像')]:
            if not os.path.exists(path):
                raise FileNotFoundError(f"❌ {name}目录不存在: {path}")
        
        # 创建评估器
        evaluator = YOLOEval(
            gt_dir=gt_dir,
            pred_dir=pred_dir,
            img_dir=img_dir,
            class_names=class_names or [],
            conf_threshold=conf_threshold
        )
        
        results = {}
        
        # SAFit评估
        print("\n📊 执行SAFit评估...")
        evaluator.evaluate(metric='safit')
        evaluator.accumulate(metric='safit')
        safit_stats = evaluator.summarize(metric='safit')
        results['safit'] = {
            'stats': safit_stats,
            'AP_0.5_0.95': safit_stats[0],
            'AP_0.5': safit_stats[1],
            'AP_0.75': safit_stats[2],
            'AP_tiny': safit_stats[4],
            'AP_small': safit_stats[5]
        }
        
        # IoU评估（用于对比）
        print("\n📊 执行IoU评估（对比用）...")
        evaluator.evaluate(metric='iou')
        evaluator.accumulate(metric='iou')
        iou_stats = evaluator.summarize(metric='iou')
        results['iou'] = {
            'stats': iou_stats,
            'AP_0.5_0.95': iou_stats[0],
            'AP_0.5': iou_stats[1],
            'AP_0.75': iou_stats[2],
            'AP_tiny': iou_stats[4],
            'AP_small': iou_stats[5]
        }
        
        # 保存结果
        if save_results:
            self._save_evaluation_results(results, evaluator)
        
        # 打印结果摘要
        self._print_results_summary(results)
        
        return results
    
    def _save_evaluation_results(self, results, evaluator):
        """保存详细评估结果"""
        timestamp = pd.Timestamp.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存JSON格式结果
        import datetime
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        json_file = self.results_dir / f"safit_evaluation_{timestamp}.json"
        with open(json_file, 'w') as f:
            # 转换numpy数组为列表以便JSON序列化
            json_results = {}
            for metric, data in results.items():
                json_results[metric] = {
                    k: v.tolist() if isinstance(v, np.ndarray) else v
                    for k, v in data.items()
                }
            json.dump(json_results, f, indent=2)
        
        print(f"💾 详细结果已保存到: {json_file}")
    
    def _print_results_summary(self, results):
        """打印结果摘要"""
        print("\n" + "="*80)
        print("📈 SAFit vs IoU 评估结果对比")
        print("="*80)
        
        safit = results['safit']
        iou = results['iou']
        
        print(f"{'指标':<20} {'SAFit':<12} {'IoU':<12} {'提升':<12}")
        print("-" * 60)
        
        metrics = [
            ('AP@[0.5:0.95]', 'AP_0.5_0.95'),
            ('AP@0.5', 'AP_0.5'),
            ('AP@0.75', 'AP_0.75'),
            ('AP_tiny', 'AP_tiny'),
            ('AP_small', 'AP_small')
        ]
        
        for name, key in metrics:
            safit_val = safit[key]
            iou_val = iou[key]
            improvement = safit_val - iou_val
            improvement_pct = (improvement / iou_val * 100) if iou_val > 0 else 0
            
            print(f"{name:<20} {safit_val:<12.3f} {iou_val:<12.3f} {improvement_pct:+.1f}%")
        
        print("\n💡 关键观察:")
        if safit['AP_tiny'] > iou['AP_tiny']:
            print(f"✅ SAFit在小目标检测上表现更好 (tiny目标提升 {((safit['AP_tiny']/iou['AP_tiny']-1)*100):+.1f}%)")
        else:
            print("⚠️  在小目标检测上，SAFit和IoU结果接近")
            
        print(f"🎯 推荐在论文中报告SAFit AP@[0.5:0.95]: {safit['AP_0.5_0.95']:.3f}")

def convert_predictions_to_yolo_format(predictions, output_dir, img_names):
    """
    将其他格式的预测结果转换为YOLO格式
    
    Args:
        predictions: 预测结果，格式为 {img_name: [{'class_id': int, 'confidence': float, 'bbox': [x1,y1,x2,y2]}]}
        output_dir: 输出目录
        img_names: 图像名称列表
    """
    os.makedirs(output_dir, exist_ok=True)
    
    for img_name in img_names:
        output_file = os.path.join(output_dir, f"{img_name}.txt")
        
        if img_name not in predictions:
            # 创建空文件
            open(output_file, 'w').close()
            continue
            
        with open(output_file, 'w') as f:
            for pred in predictions[img_name]:
                # 假设bbox是[x1, y1, x2, y2]格式，需要转换为YOLO格式
                x1, y1, x2, y2 = pred['bbox']
                x_center = (x1 + x2) / 2
                y_center = (y1 + y2) / 2
                width = x2 - x1
                height = y2 - y1
                
                # 写入YOLO格式: class_id confidence x_center y_center width height
                f.write(f"{pred['class_id']} {pred['confidence']:.6f} "
                       f"{x_center:.6f} {y_center:.6f} {width:.6f} {height:.6f}\n")

# 使用示例
if __name__ == "__main__":
    # 创建评估器
    evaluator = SAFitEvaluator()
    
    # 配置路径（根据您的项目结构修改）
    gt_dir = "data/labels/gt"
    pred_dir = "data/labels/pred"  
    img_dir = "data/images"
    class_names = ['person', 'car', 'bicycle']  # 修改为您的类别
    
    # 执行评估
    try:
        results = evaluator.evaluate_predictions(
            gt_dir=gt_dir,
            pred_dir=pred_dir,
            img_dir=img_dir,
            class_names=class_names,
            conf_threshold=0.001
        )
        
        print("\n✅ SAFit评估完成！")
        print(f"🎯 主要结果: SAFit AP@[0.5:0.95] = {results['safit']['AP_0.5_0.95']:.3f}")
        
    except FileNotFoundError as e:
        print(f"❌ 错误: {e}")
        print("请确保数据路径正确，并且预测结果已生成")
    except Exception as e:
        print(f"❌ 评估过程中出现错误: {e}")
