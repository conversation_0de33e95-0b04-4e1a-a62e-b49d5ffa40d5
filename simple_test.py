#!/usr/bin/env python3
"""
简化的SAFit评估测试脚本
用于验证评估工具是否正常工作
"""

import os
import sys
import numpy as np
import tempfile
from pathlib import Path

def test_yoloeval_import():
    """测试yoloeval模块是否能正常导入"""
    try:
        sys.path.append('codes')
        from metric.yoloeval import YOLOEval
        print("✅ yoloeval模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ yoloeval模块导入失败: {e}")
        return False

def create_sample_data():
    """创建示例数据用于测试"""
    print("📁 创建示例测试数据...")
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    temp_path = Path(temp_dir)
    
    # 创建目录结构
    images_dir = temp_path / "images"
    labels_dir = temp_path / "labels"
    pred_dir = temp_path / "predictions"
    
    for dir_path in [images_dir, labels_dir, pred_dir]:
        dir_path.mkdir(parents=True, exist_ok=True)
    
    # 创建示例文件
    for i in range(3):
        img_name = f"test_{i:03d}"
        
        # 创建虚拟图像文件（空文件，只是为了测试）
        (images_dir / f"{img_name}.jpg").touch()
        
        # 创建GT标签（YOLO格式）
        with open(labels_dir / f"{img_name}.txt", 'w') as f:
            # 每个图像1-2个目标
            num_objects = np.random.randint(1, 3)
            for _ in range(num_objects):
                class_id = np.random.randint(0, 3)
                x_center = np.random.uniform(0.2, 0.8)
                y_center = np.random.uniform(0.2, 0.8)
                width = np.random.uniform(0.05, 0.2)
                height = np.random.uniform(0.05, 0.2)
                f.write(f"{class_id} {x_center:.6f} {y_center:.6f} {width:.6f} {height:.6f}\n")
        
        # 创建预测结果（YOLO格式）
        with open(pred_dir / f"{img_name}.txt", 'w') as f:
            # 每个图像1-3个预测
            num_preds = np.random.randint(1, 4)
            for _ in range(num_preds):
                class_id = np.random.randint(0, 3)
                confidence = np.random.uniform(0.3, 0.9)
                x_center = np.random.uniform(0.2, 0.8)
                y_center = np.random.uniform(0.2, 0.8)
                width = np.random.uniform(0.05, 0.2)
                height = np.random.uniform(0.05, 0.2)
                f.write(f"{class_id} {confidence:.6f} {x_center:.6f} {y_center:.6f} {width:.6f} {height:.6f}\n")
    
    print(f"✅ 创建了 3 张测试图像和标签")
    return str(images_dir), str(labels_dir), str(pred_dir)

def test_safit_evaluation():
    """测试SAFit评估功能"""
    print("\n🧪 测试SAFit评估功能...")
    
    try:
        # 导入评估模块
        sys.path.append('codes')
        from metric.yoloeval import YOLOEval
        
        # 创建测试数据
        img_dir, gt_dir, pred_dir = create_sample_data()
        
        # 创建评估器
        class_names = ['person', 'car', 'bicycle']
        evaluator = YOLOEval(
            gt_dir=gt_dir,
            pred_dir=pred_dir,
            img_dir=img_dir,
            class_names=class_names,
            conf_threshold=0.001
        )
        
        print("📊 执行SAFit评估...")
        
        # SAFit评估
        evaluator.evaluate(metric='safit')
        evaluator.accumulate(metric='safit')
        safit_stats = evaluator.summarize(metric='safit')
        
        print("📊 执行IoU评估...")
        
        # IoU评估
        evaluator.evaluate(metric='iou')
        evaluator.accumulate(metric='iou')
        iou_stats = evaluator.summarize(metric='iou')
        
        # 显示结果
        print("\n" + "="*60)
        print("📈 测试评估结果:")
        print("="*60)
        print(f"SAFit AP@[0.5:0.95]: {safit_stats[0]:.3f}")
        print(f"SAFit AP@0.5:       {safit_stats[1]:.3f}")
        print(f"IoU AP@[0.5:0.95]:   {iou_stats[0]:.3f}")
        print(f"IoU AP@0.5:         {iou_stats[1]:.3f}")
        
        # 清理临时文件
        import shutil
        shutil.rmtree(Path(img_dir).parent)
        
        print("✅ SAFit评估测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ SAFit评估测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_template():
    """测试配置文件模板"""
    print("\n🧪 测试配置文件模板...")
    
    try:
        if os.path.exists('config_template.yaml'):
            with open('config_template.yaml', 'r') as f:
                content = f.read()
                if 'model_path' in content and 'test_images_dir' in content:
                    print("✅ 配置文件模板格式正确")
                    return True
                else:
                    print("❌ 配置文件模板缺少必要字段")
                    return False
        else:
            print("⚠️ 配置文件模板不存在，但这不影响核心功能")
            return True
            
    except Exception as e:
        print(f"❌ 配置文件测试失败: {e}")
        return False

def test_universal_evaluator():
    """测试通用评估器导入"""
    print("\n🧪 测试通用评估器...")
    
    try:
        if os.path.exists('universal_safit_evaluator.py'):
            # 简单的语法检查
            with open('universal_safit_evaluator.py', 'r') as f:
                content = f.read()
                if 'UniversalSAFitEvaluator' in content:
                    print("✅ 通用评估器文件存在且格式正确")
                    return True
                else:
                    print("❌ 通用评估器文件格式错误")
                    return False
        else:
            print("❌ 通用评估器文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 通用评估器测试失败: {e}")
        return False

def check_required_files():
    """检查必要文件是否存在"""
    print("\n🔍 检查必要文件...")
    
    required_files = [
        'codes/metric/yoloeval.py',
        'codes/loss/loss.py',
        'codes/loss/utils.py'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
        else:
            print(f"✅ {file_path}")
    
    if missing_files:
        print("\n❌ 缺少以下必要文件:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False
    else:
        print("✅ 所有必要文件都存在")
        return True

def main():
    """主测试函数"""
    print("🚀 开始SAFit评估工具测试...")
    print("=" * 60)
    
    tests = [
        ("检查必要文件", check_required_files),
        ("yoloeval模块导入", test_yoloeval_import),
        ("SAFit评估功能", test_safit_evaluation),
        ("配置文件模板", test_config_template),
        ("通用评估器", test_universal_evaluator),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        if test_func():
            passed += 1
        else:
            print(f"⚠️ {test_name} 测试失败")
    
    print("\n" + "="*60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过! SAFit评估工具可以正常使用。")
        print("\n📝 下一步:")
        print("1. 确保您的GT标签是YOLO格式（归一化坐标）")
        print("2. 修改 config_template.yaml 中的路径")
        print("3. 运行: python universal_safit_evaluator.py --config config.yaml")
        return True
    else:
        print("⚠️ 部分测试失败，请检查配置和依赖。")
        print("\n🔧 可能的解决方案:")
        print("1. 确保已复制所有必要文件到当前目录")
        print("2. 检查Python环境和依赖包")
        print("3. 确认codes目录结构正确")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ 测试完成，工具可以使用!")
    else:
        print("\n❌ 测试失败，请检查问题后重试")
